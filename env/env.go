package env

import (
	"os"
	"strings"
)

/**
 * GetStringDefault returns a string value from the environment variable for a specific key or returns the expected default value.
 *
 * Parameters:
 *     - key (string): The key of the environment variable to retrieve.
 *     - def (string): The default value to return if the environment variable is not set or is empty.
 *
 * Returns:
 *     - string: The value of the environment variable if it is set and not empty, otherwise the default value.
 */
func GetStringDefault(key string, def string) string {
	val := os.Getenv(string(key))
	if len(val) == 0 {
		return def
	}
	return val
}

/*
IsProduction returns a boolean value indicating whether the application is running in production mode.

The function uses the GetStringDefault function to retrieve the value of the "ENVIRONMENT" environment variable. If the value is empty or is not equal to "production", the function assumes the application is not in production mode and returns false. Otherwise, it compares the value with "production" and returns true if they are equal.

Returns:
- bool: true if the application is in production mode, false otherwise.
*/
func IsProduction() bool {
	return GetStringDefault("ENVIRONMENT", "development") == "production"
}

/**
 * EnableSwagger is a function that returns a boolean value indicating whether <PERSON>wagger is enabled or not.
 *
 * It uses the GetStringDefault function to retrieve the value of the "ENABLE_SWAGGER" environment variable.
 * If the value is not set or is empty, it returns true by default.
 *
 * Returns:
 *     - true if Swagger is enabled
 *     - false if Swagger is disabled
 */
func EnableSwagger() bool {
	return GetStringDefault("ENABLE_SWAGGER", "true") == "true"
}

/**
 * EnableCORS returns a boolean value indicating whether CORS (Cross-Origin Resource Sharing) is enabled or not.
 * It uses the GetStringDefault function to retrieve the value of the "ENABLE_CORS" environment variable.
 * If the value is not set or is not equal to "true", it returns false. Otherwise, it returns true.
 *
 * @return bool - true if CORS is enabled, false otherwise.
 */
func EnableCORS() bool {
	return GetStringDefault("ENABLE_CORS", "true") == "true"
}

/*
SecureListener returns a boolean value indicating whether the HTTP listener should be secure or not.

The function uses the GetStringDefault function to retrieve the value of the "SECURE_HTTP_LISTENER" environment variable. If the value is "true", SecureListener returns true. Otherwise, it returns false.

Returns:
- bool: true if the listener should be secure, false otherwise.
*/
func SecureListener() bool {
	return GetStringDefault("SECURE_LISTENER", "false") == "true"
}

/*
TrustedProxies returns a list of trusted proxies.

This function retrieves the value of the environment variable "SECURE_LISTEN_ADDRESSES" using the GetStringDefault function. If the environment variable is not set, the default value "127.0.0.1" is used. The value is then split into a list of strings using the comma as the delimiter.

Returns:
- A list of trusted proxies as strings.

Example:
trustedProxies := TrustedProxies()
fmt.Println(trustedProxies) // Output: ["127.0.0.1"]

Note:
- The GetStringDefault function is used to retrieve the environment variable value with a default fallback.
- The list of trusted proxies is split using the strings.Split function.
*/
func TrustedProxies() []string {
	return strings.Split(GetStringDefault("TRUSTED_PROXIES", "127.0.0.1"), ",")
}
