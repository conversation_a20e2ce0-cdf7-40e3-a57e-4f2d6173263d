// Package totp implements Time-based One-Time Password (TOTP) functionality.
package totp

import (
	"time"

	"github.com/pquerna/otp"
	"github.com/pquerna/otp/totp"
)

// TOTPOptions represents the configuration options for generating and validating TOTP codes.
// It contains all the necessary parameters to create and verify time-based one-time passwords.
type TOTPOptions struct {
	// Secret is the shared secret key used to generate and validate TOTP codes.
	// This should be unique for each user and kept secure.
	Secret string `json:"secret"`

	// Period is the time step in seconds for TOTP code generation.
	// Common values are 30 seconds or 300 seconds (5 minutes).
	// The TOTP code changes after each period.
	Period uint `json:"period"`

	// AccountName is the identifier for the account (typically an email or username).
	// This is used to associate the TOTP code with a specific user account.
	AccountName string `json:"account_name"`

	// Issuer identifies the service or application that issued the TOTP.
	// This helps users identify which service a TOTP code is for when using
	// authenticator apps that support multiple accounts.
	Issuer string `json:"issuer"`

	// Digits specifies the number of digits in the generated TOTP code.
	// Common values are 6 or 8. If not specified, defaults to 6.
	Digits otp.Digits `json:"digits,omitempty"`

	// Algorithm specifies the hashing algorithm used for TOTP generation.
	// Common values are SHA1, SHA256, or SHA512. If not specified, defaults to SHA1.
	Algorithm otp.Algorithm `json:"algorithm,omitempty"`

	// Skew specifies the number of periods before and after the current time to allow.
	// This helps account for clock skew between the server and client.
	// If not specified, defaults to 1.
	Skew uint `json:"skew,omitempty"`
}

// GenerateTOTPCode generates a TOTP code for the specified account with the configured options.
//
// This function creates a TOTP code based on the provided options. The code is generated using
// the current time and will be valid for the duration specified by the Period parameter in the options.
// The number of digits and hashing algorithm can be customized through the options.
//
// Parameters:
//   - options: The TOTPOptions containing the configuration for TOTP generation.
//
// Returns:
//   - string: The generated TOTP code.
//   - error: An error if the generation fails, or nil if successful.
//
// Example:
//
//	// Basic usage with defaults (6 digits, SHA1, 5-minute period)
//	options := totp.TOTPOptions{
//	    Secret:      "your-secret-key",
//	    Period:      300, // 5 minutes
//	    AccountName: "<EMAIL>",
//	    Issuer:      "YourApp",
//	}
//
//	// Custom configuration (8 digits, SHA256, 30-second period)
//	customOptions := totp.TOTPOptions{
//	    Secret:      "your-secret-key",
//	    Period:      30,
//	    AccountName: "<EMAIL>",
//	    Issuer:      "YourApp",
//	    Digits:      otp.DigitsEight,
//	    Algorithm:   otp.AlgorithmSHA256,
//	    Skew:        2,
//	}
//	code, err := totp.GenerateTOTPCode(options)
func GenerateTOTPCode(options TOTPOptions) (string, error) {
	// Set default values if not specified
	digits := options.Digits
	if digits == 0 {
		digits = otp.DigitsSix
	}

	algorithm := options.Algorithm
	if algorithm == 0 {
		algorithm = otp.AlgorithmSHA1
	}

	skew := options.Skew
	if skew == 0 {
		skew = 1
	}

	period := options.Period
	if period == 0 {
		period = 300 // Default to 5 minutes
	}

	opts := totp.GenerateOpts{
		Issuer:      options.Issuer,
		AccountName: options.AccountName,
		Period:      period,
		Secret:      []byte(options.Secret),
		Digits:      digits,
		Algorithm:   algorithm,
	}

	key, err := totp.Generate(opts)
	if err != nil {
		return "", err
	}

	// Generate current TOTP code
	code, err := totp.GenerateCodeCustom(key.Secret(), time.Now(), totp.ValidateOpts{
		Period:    period,
		Skew:      skew,
		Digits:    digits,
		Algorithm: algorithm,
	})
	if err != nil {
		return "", err
	}
	return code, nil
}

// VerifyTOTPCode verifies a TOTP code for the specified account.
//
// This function validates whether the provided TOTP code is correct for the given account
// and current time. It uses the configuration specified in the options parameter.
// The function allows for a configurable time skew to account for clock differences.
//
// Parameters:
//   - options: The TOTPOptions containing the configuration for TOTP validation.
//   - code: The TOTP code to verify.
//
// Returns:
//   - bool: true if the code is valid, false otherwise.
//   - error: An error if the validation process fails, or nil if successful.
//
// Example:
//
//	// Basic usage with defaults
//	options := totp.TOTPOptions{
//	    Secret:      "your-secret-key",
//	    Period:      300, // 5 minutes
//	    AccountName: "<EMAIL>",
//	    Issuer:      "YourApp",
//	}
//
//	// Custom configuration
//	customOptions := totp.TOTPOptions{
//	    Secret:      "your-secret-key",
//	    Period:      30,
//	    AccountName: "<EMAIL>",
//	    Issuer:      "YourApp",
//	    Digits:      otp.DigitsEight,
//	    Algorithm:   otp.AlgorithmSHA256,
//	    Skew:        2, // Allow 2 periods before and after
//	}
//	valid, err := totp.VerifyTOTPCode(options, "123456")
func VerifyTOTPCode(options TOTPOptions, code string) (bool, error) {
	// Set default values if not specified
	digits := options.Digits
	if digits == 0 {
		digits = otp.DigitsSix
	}

	algorithm := options.Algorithm
	if algorithm == 0 {
		algorithm = otp.AlgorithmSHA1
	}

	skew := options.Skew
	if skew == 0 {
		skew = 1
	}

	period := options.Period
	if period == 0 {
		period = 300 // Default to 5 minutes
	}

	return totp.ValidateCustom(code, options.Secret, time.Now(), totp.ValidateOpts{
		Period:    period,
		Skew:      skew,
		Digits:    digits,
		Algorithm: algorithm,
	})
}
