/*
Package totp implements Time-based One-Time Password (TOTP) functionality as specified in RFC 6238.

TOTP is an extension of the HMAC-based One-Time Password (HOTP) algorithm that generates
a one-time password by taking uniqueness from the current time. It is commonly used for
two-factor authentication (2FA) systems.

# Overview

This package provides:

  - Generation of TOTP codes with configurable parameters
  - Verification of TOTP codes
  - Customizable digit count (6 or 8 digits)
  - Configurable time periods (30 seconds, 5 minutes, etc.)
  - Multiple hashing algorithm options (SHA1, SHA256, SHA512)
  - Account-specific TOTP validation
  - Redis-based storage for TOTP secrets

# Basic Usage

To generate a TOTP code:

    // Create TOTP options
    options := totp.TOTPOptions{
        Secret:      "your-secret-key",
        Period:      300, // 5 minutes
        AccountName: "<EMAIL>",
        Issuer:      "YourApp",
        Digits:      6,   // 6-digit code
    }

    // Generate a TOTP code
    code, err := totp.GenerateTOTPCode(options)
    if err != nil {
        log.Fatalf("Failed to generate TOTP code: %v", err)
    }

To verify a TOTP code:

    // Verify a TOTP code
    valid, err := totp.VerifyTOTPCode(options, code)
    if err != nil {
        log.Fatalf("Error verifying TOTP code: %v", err)
    }
    if valid {
        log.Println("TOTP code is valid")
    } else {
        log.Println("TOTP code is invalid")
    }

# Advanced Usage with Redis Storage

The package provides a Redis-based service implementation for storing and managing TOTP secrets:

    // Create a Redis client
    redisClient := redis.NewClient(&redis.Options{
        Addr: "localhost:6379",
    })
    defer redisClient.Close()

    // Create a TOTP service with custom configuration
    totpConfig := totp.TOTPServiceConfig{
        Issuer:    "YourApp",
        Digits:    8,           // 8-digit codes
        Period:    30,          // 30-second validity
        Algorithm: otp.AlgorithmSHA256,
    }
    totpService := totp.NewRedisTOTPService(redisClient, "app", totpConfig)

    // Generate a TOTP code with 5 minutes expiration
    ctx := context.Background()
    code, err := totpService.GenerateTOTP(ctx, "<EMAIL>", 5*time.Minute)
    if err != nil {
        log.Fatalf("Failed to generate TOTP: %v", err)
    }

    // Verify a TOTP code
    err = totpService.VerifyTOTP(ctx, "<EMAIL>", code)
    if err != nil {
        log.Fatalf("TOTP verification failed: %v", err)
    }

# Security Considerations

  - TOTP secrets should be stored securely and transmitted over secure channels
  - The default hashing algorithm is SHA1, which is the standard for TOTP
  - For higher security, SHA256 or SHA512 can be used
  - The default period is 5 minutes, which provides a good balance between security and usability
  - For stricter security, a shorter period (e.g., 30 seconds) can be used
  - The default digit count is 6, but 8 digits provide higher security
  - TOTP codes should be one-time use and invalidated after successful verification
*/
package totp
