// Package totp implements Time-based One-Time Password (TOTP) functionality.
package totp

import (
	"context"
	"crypto/rand"
	"encoding/base32"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/pquerna/otp"
	"github.com/redis/go-redis/v9"
)

// Common errors returned by the TOTP service
var (
	// ErrInvalidTOTP is returned when the TOTP code is invalid
	ErrInvalidTOTP = errors.New("invalid TOTP code")
	
	// ErrTOTPExpired is returned when the TOTP code has expired
	ErrTOTPExpired = errors.New("TOTP code has expired")
	
	// ErrInvalidAccount is returned when the account name is invalid
	ErrInvalidAccount = errors.New("invalid account name")
)

// TOTPServiceConfig represents the configuration for the TOTP service.
// It contains default values for TOTP generation and validation.
type TOTPServiceConfig struct {
	// Issuer identifies the service or application that issued the TOTP.
	Issuer string

	// Digits specifies the number of digits in the generated TOTP code.
	// Common values are 6 or 8. If not specified, defaults to 6.
	Digits otp.Digits

	// Period is the time step in seconds for TOTP code generation.
	// Common values are 30 seconds or 300 seconds (5 minutes).
	Period uint

	// Algorithm specifies the hashing algorithm used for TOTP generation.
	// Common values are SHA1, SHA256, or SHA512. If not specified, defaults to SHA1.
	Algorithm otp.Algorithm

	// Skew specifies the number of periods before and after the current time to allow.
	// This helps account for clock skew between the server and client.
	Skew uint
}

// DefaultTOTPServiceConfig returns a default configuration for the TOTP service.
func DefaultTOTPServiceConfig() TOTPServiceConfig {
	return TOTPServiceConfig{
		Issuer:    "DelsaPay",
		Digits:    otp.DigitsSix,
		Period:    300, // 5 minutes
		Algorithm: otp.AlgorithmSHA1,
		Skew:      1,
	}
}

// TOTPService provides methods for generating and verifying TOTP codes
type TOTPService interface {
	// GenerateTOTP generates a new TOTP code for the given account name with the specified expiration
	GenerateTOTP(ctx context.Context, accountName string, expiration time.Duration) (string, error)
	
	// VerifyTOTP verifies a TOTP code for the given account name
	VerifyTOTP(ctx context.Context, accountName, code string) error
}

// RedisTOTPService implements TOTPService using Redis for storage
type RedisTOTPService struct {
	redis  *redis.Client
	prefix string
	config TOTPServiceConfig
}

// TOTPData represents the data stored in Redis for a TOTP code
type TOTPData struct {
	// Options contains the TOTP configuration
	Options TOTPOptions `json:"options"`
	
	// ExpiresAt is the time when the TOTP expires
	ExpiresAt time.Time `json:"expires_at"`
}

// NewRedisTOTPService creates a new RedisTOTPService with the specified configuration
//
// Parameters:
//   - redisClient: The Redis client to use for storage
//   - keyPrefix: The prefix to use for Redis keys
//   - config: The configuration for the TOTP service
//
// Returns:
//   - TOTPService: The TOTP service implementation
func NewRedisTOTPService(redisClient *redis.Client, keyPrefix string, config TOTPServiceConfig) TOTPService {
	return &RedisTOTPService{
		redis:  redisClient,
		prefix: keyPrefix,
		config: config,
	}
}

// GenerateTOTP generates a new TOTP code for the given account name with the specified expiration
//
// This function generates a new random secret, creates a TOTP code with it, and stores the
// secret in Redis with the specified expiration time. The TOTP code is returned to the caller.
//
// Parameters:
//   - ctx: The context for the operation
//   - accountName: The name of the account (e.g., email or username)
//   - expiration: The duration after which the TOTP code will expire
//
// Returns:
//   - string: The generated TOTP code
//   - error: An error if the generation fails, or nil if successful
func (s *RedisTOTPService) GenerateTOTP(ctx context.Context, accountName string, expiration time.Duration) (string, error) {
	if accountName == "" {
		return "", ErrInvalidAccount
	}

	// Generate a random secret
	secret, err := generateRandomSecret(20)
	if err != nil {
		return "", fmt.Errorf("failed to generate random secret: %w", err)
	}
	
	// Create TOTP options
	options := TOTPOptions{
		Secret:      secret,
		Period:      s.config.Period,
		AccountName: accountName,
		Issuer:      s.config.Issuer,
		Digits:      s.config.Digits,
		Algorithm:   s.config.Algorithm,
		Skew:        s.config.Skew,
	}
	
	// Generate the TOTP code
	code, err := GenerateTOTPCode(options)
	if err != nil {
		return "", fmt.Errorf("failed to generate TOTP code: %w", err)
	}
	
	// Store the data in Redis
	data := TOTPData{
		Options:   options,
		ExpiresAt: time.Now().Add(expiration),
	}
	
	dataJSON, err := json.Marshal(data)
	if err != nil {
		return "", fmt.Errorf("failed to marshal TOTP data: %w", err)
	}
	
	key := s.getRedisKey(accountName)
	err = s.redis.Set(ctx, key, dataJSON, expiration).Err()
	if err != nil {
		return "", fmt.Errorf("failed to store TOTP data in Redis: %w", err)
	}
	
	return code, nil
}

// VerifyTOTP verifies a TOTP code for the given account name
//
// This function retrieves the TOTP data from Redis, checks if it has expired,
// and verifies the provided TOTP code against the stored secret.
//
// Parameters:
//   - ctx: The context for the operation
//   - accountName: The name of the account (e.g., email or username)
//   - code: The TOTP code to verify
//
// Returns:
//   - error: An error if the verification fails, or nil if successful
func (s *RedisTOTPService) VerifyTOTP(ctx context.Context, accountName, code string) error {
	if accountName == "" {
		return ErrInvalidAccount
	}
	
	// Get the data from Redis
	key := s.getRedisKey(accountName)
	dataJSON, err := s.redis.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return ErrInvalidTOTP
		}
		return fmt.Errorf("failed to get TOTP data from Redis: %w", err)
	}
	
	// Unmarshal the data
	var data TOTPData
	err = json.Unmarshal([]byte(dataJSON), &data)
	if err != nil {
		return fmt.Errorf("failed to unmarshal TOTP data: %w", err)
	}
	
	// Check if the TOTP has expired
	if time.Now().After(data.ExpiresAt) {
		return ErrTOTPExpired
	}
	
	// Verify the TOTP code
	valid, err := VerifyTOTPCode(data.Options, code)
	if err != nil {
		return fmt.Errorf("failed to verify TOTP code: %w", err)
	}
	
	if !valid {
		return ErrInvalidTOTP
	}
	
	// Delete the TOTP data from Redis after successful verification
	err = s.redis.Del(ctx, key).Err()
	if err != nil {
		// Log the error but don't fail the verification
		fmt.Printf("Failed to delete TOTP data from Redis: %v\n", err)
	}
	
	return nil
}

// getRedisKey returns the Redis key for the given account name
func (s *RedisTOTPService) getRedisKey(accountName string) string {
	return fmt.Sprintf("%s:totp:%s", s.prefix, accountName)
}

// generateRandomSecret generates a random secret of the specified length
func generateRandomSecret(length int) (string, error) {
	// Generate random bytes
	bytes := make([]byte, length)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", err
	}
	
	// Encode as base32
	return base32.StdEncoding.WithPadding(base32.NoPadding).EncodeToString(bytes), nil
}
