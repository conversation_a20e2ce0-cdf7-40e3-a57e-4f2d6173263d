package totp

import (
	"context"
	"testing"
	"time"

	"github.com/alicebob/miniredis/v2"
	"github.com/pquerna/otp"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupRedis(t *testing.T) (*redis.Client, func()) {
	mr, err := miniredis.Run()
	require.NoError(t, err)

	client := redis.NewClient(&redis.Options{
		Addr: mr.Addr(),
	})

	return client, func() {
		client.Close()
		mr.Close()
	}
}

func TestRedisTOTPService_GenerateTOTP(t *testing.T) {
	redisClient, cleanup := setupRedis(t)
	defer cleanup()

	// Test with default configuration
	defaultConfig := DefaultTOTPServiceConfig()
	service := NewRedisTOTPService(redisClient, "test", defaultConfig)
	ctx := context.Background()

	// Test with valid account name
	accountName := "<EMAIL>"
	expiration := 5 * time.Minute

	code, err := service.GenerateTOTP(ctx, accountName, expiration)
	require.NoError(t, err)
	assert.Len(t, code, 6) // Default is 6 digits

	// Test with custom configuration (8 digits)
	customConfig := TOTPServiceConfig{
		Issuer:    "TestApp",
		Digits:    otp.DigitsEight,
		Period:    30,
		Algorithm: otp.AlgorithmSHA256,
		Skew:      2,
	}
	customService := NewRedisTOTPService(redisClient, "test", customConfig)

	code, err = customService.GenerateTOTP(ctx, accountName, expiration)
	require.NoError(t, err)
	assert.Len(t, code, 8) // Custom config uses 8 digits

	// Test with empty account name
	_, err = service.GenerateTOTP(ctx, "", expiration)
	assert.Equal(t, ErrInvalidAccount, err)
}

func TestRedisTOTPService_VerifyTOTP(t *testing.T) {
	redisClient, cleanup := setupRedis(t)
	defer cleanup()

	// Test with default configuration
	defaultConfig := DefaultTOTPServiceConfig()
	service := NewRedisTOTPService(redisClient, "test", defaultConfig)
	ctx := context.Background()

	// Generate a TOTP code
	accountName := "<EMAIL>"
	expiration := 5 * time.Minute

	code, err := service.GenerateTOTP(ctx, accountName, expiration)
	require.NoError(t, err)

	// Test with valid code
	err = service.VerifyTOTP(ctx, accountName, code)
	assert.NoError(t, err)

	// Test with invalid code
	err = service.VerifyTOTP(ctx, accountName, "000000")
	assert.Equal(t, ErrInvalidTOTP, err)

	// Test with empty account name
	err = service.VerifyTOTP(ctx, "", code)
	assert.Equal(t, ErrInvalidAccount, err)

	// Test with non-existent account
	err = service.VerifyTOTP(ctx, "<EMAIL>", code)
	assert.Equal(t, ErrInvalidTOTP, err)
}

func TestRedisTOTPService_ExpiredTOTP(t *testing.T) {
	redisClient, cleanup := setupRedis(t)
	defer cleanup()

	mr := redisClient.Options().Addr

	miniRedis := miniredis.NewMiniRedis()
	err := miniRedis.StartAddr(mr)
	require.NoError(t, err)
	defer miniRedis.Close()

	// Test with default configuration
	defaultConfig := DefaultTOTPServiceConfig()
	service := NewRedisTOTPService(redisClient, "test", defaultConfig)
	ctx := context.Background()

	// Generate a TOTP code
	accountName := "<EMAIL>"
	expiration := 5 * time.Minute

	code, err := service.GenerateTOTP(ctx, accountName, expiration)
	require.NoError(t, err)

	// Fast-forward time in miniredis
	miniRedis.FastForward(10 * time.Minute)

	// Test with expired code
	err = service.VerifyTOTP(ctx, accountName, code)
	assert.Equal(t, ErrInvalidTOTP, err) // The key should be expired in Redis
}

func TestCustomTOTPConfiguration(t *testing.T) {
	// Test different digit counts
	sixDigitOptions := TOTPOptions{
		Secret:      "JBSWY3DPEHPK3PXP",
		Period:      30,
		AccountName: "<EMAIL>",
		Issuer:      "TestApp",
		Digits:      otp.DigitsSix,
		Algorithm:   otp.AlgorithmSHA1,
	}

	eightDigitOptions := TOTPOptions{
		Secret:      "JBSWY3DPEHPK3PXP",
		Period:      30,
		AccountName: "<EMAIL>",
		Issuer:      "TestApp",
		Digits:      otp.DigitsEight,
		Algorithm:   otp.AlgorithmSHA1,
	}

	// Generate codes with different digit counts
	sixDigitCode, err := GenerateTOTPCode(sixDigitOptions)
	require.NoError(t, err)
	assert.Len(t, sixDigitCode, 6)

	eightDigitCode, err := GenerateTOTPCode(eightDigitOptions)
	require.NoError(t, err)
	assert.Len(t, eightDigitCode, 8)

	// Test different periods
	shortPeriodOptions := TOTPOptions{
		Secret:      "JBSWY3DPEHPK3PXP",
		Period:      30, // 30 seconds
		AccountName: "<EMAIL>",
		Issuer:      "TestApp",
		Digits:      otp.DigitsSix,
		Algorithm:   otp.AlgorithmSHA1,
	}

	longPeriodOptions := TOTPOptions{
		Secret:      "JBSWY3DPEHPK3PXP",
		Period:      300, // 5 minutes
		AccountName: "<EMAIL>",
		Issuer:      "TestApp",
		Digits:      otp.DigitsSix,
		Algorithm:   otp.AlgorithmSHA1,
	}

	// Generate codes with different periods
	shortPeriodCode, err := GenerateTOTPCode(shortPeriodOptions)
	require.NoError(t, err)

	longPeriodCode, err := GenerateTOTPCode(longPeriodOptions)
	require.NoError(t, err)

	// The codes should be different due to different periods
	assert.NotEqual(t, shortPeriodCode, longPeriodCode)
}
