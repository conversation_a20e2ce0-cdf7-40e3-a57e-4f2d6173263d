# TOTP Example Application

This is an example application demonstrating how to use the TOTP package for generating and validating time-based one-time passwords with customizable options.

## Prerequisites

- Go 1.16 or later
- Redis server running on localhost:6379

## Running the Example

```bash
go run main.go
```

## What the Example Does

1. Connects to a Redis server
2. Creates TOTP services with different configurations:
   - Default configuration (6 digits, SHA1, 5-minute period)
   - Custom configuration (8 digits, SHA256, 30-second period)
3. Generates TOTP codes with each configuration
4. Verifies the TOTP codes
5. Demonstrates direct use of TOTPOptions without Redis

## Expected Output

```
=== TOTP Example Application ===
This example demonstrates how to use the TOTP package with customizable options.

=== Example 1: Default Configuration ===
Generated 6-digit TOTP <NAME_EMAIL>: 123456
This code will expire in 5m0s.

Default TOTP code verified successfully!

=== Example 2: Custom Configuration ===
Generated 8-digit TOTP <NAME_EMAIL>: 12345678
This code will expire in 5m0s but is only valid for 30 seconds at a time.

Custom TOTP code verified successfully!

=== Example 3: Direct use of TOTPOptions ===
Generated TOTP code <NAME_EMAIL>: 654321

Direct TOTP code verified successfully!
```

## Code Explanation

The example demonstrates several key features of the TOTP package:

1. **Default Configuration**: The example creates a `RedisTOTPService` with default configuration (6 digits, SHA1, 5-minute period).

2. **Custom Configuration**: The example creates a `RedisTOTPService` with custom configuration (8 digits, SHA256, 30-second period).

3. **Direct Use of TOTPOptions**: The example demonstrates how to use the `TOTPOptions` struct directly without Redis storage.

4. **Configurable Options**: The example shows how to configure:
   - Digit count (6 or 8 digits)
   - Time period (30 seconds, 1 minute, 5 minutes)
   - Hashing algorithm (SHA1, SHA256)
   - Skew (number of periods to allow for validation)

5. **Verification**: The example demonstrates how to verify TOTP codes with different configurations.

## Integration with Real Applications

In a real application, you would typically:

1. Generate a TOTP code when a user requests it (e.g., for login or a sensitive operation)
2. Send the TOTP code to the user via a secure channel (e.g., email or SMS)
3. Prompt the user to enter the TOTP code
4. Verify the TOTP code before allowing the operation to proceed

The TOTP package provides all the functionality needed for these steps, with the actual delivery of the code to the user being the only part that would need to be implemented separately.
