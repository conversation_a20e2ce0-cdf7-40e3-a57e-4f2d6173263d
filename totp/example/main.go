// Example application demonstrating the use of the TOTP package with customizable options
package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/liveutil/go-lib/totp"
	"github.com/pquerna/otp"
	"github.com/redis/go-redis/v9"
)

func main() {
	// Create a Redis client
	redisClient := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
	})
	defer redisClient.Close()

	// Ping Redis to check the connection
	ctx := context.Background()
	_, err := redisClient.Ping(ctx).Result()
	if err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}

	fmt.Println("=== TOTP Example Application ===")
	fmt.Println("This example demonstrates how to use the TOTP package with customizable options.")
	fmt.Println()

	// Example 1: Default Configuration (6 digits, SHA1, 5-minute period)
	fmt.Println("=== Example 1: Default Configuration ===")
	defaultConfig := totp.DefaultTOTPServiceConfig()
	defaultService := totp.NewRedisTOTPService(redisClient, "app", defaultConfig)

	// Generate a TOTP code with default configuration
	accountName := "<EMAIL>"
	expiration := 5 * time.Minute

	defaultCode, err := defaultService.GenerateTOTP(ctx, accountName, expiration)
	if err != nil {
		log.Fatalf("Failed to generate TOTP with default config: %v", err)
	}

	fmt.Printf("Generated 6-digit TOTP code for %s: %s\n", accountName, defaultCode)
	fmt.Printf("This code will expire in %s.\n", expiration)
	fmt.Println()

	// Verify the TOTP code
	err = defaultService.VerifyTOTP(ctx, accountName, defaultCode)
	if err != nil {
		log.Fatalf("Failed to verify TOTP with default config: %v", err)
	}

	fmt.Println("Default TOTP code verified successfully!")
	fmt.Println()

	// Example 2: Custom Configuration (8 digits, SHA256, 30-second period)
	fmt.Println("=== Example 2: Custom Configuration ===")
	customConfig := totp.TOTPServiceConfig{
		Issuer:    "CustomApp",
		Digits:    otp.DigitsEight,
		Period:    30, // 30 seconds
		Algorithm: otp.AlgorithmSHA256,
		Skew:      2, // Allow 2 periods before and after
	}
	customService := totp.NewRedisTOTPService(redisClient, "custom", customConfig)

	// Generate a TOTP code with custom configuration
	customCode, err := customService.GenerateTOTP(ctx, accountName, expiration)
	if err != nil {
		log.Fatalf("Failed to generate TOTP with custom config: %v", err)
	}

	fmt.Printf("Generated 8-digit TOTP code for %s: %s\n", accountName, customCode)
	fmt.Printf("This code will expire in %s but is only valid for 30 seconds at a time.\n", expiration)
	fmt.Println()

	// Verify the TOTP code
	err = customService.VerifyTOTP(ctx, accountName, customCode)
	if err != nil {
		log.Fatalf("Failed to verify TOTP with custom config: %v", err)
	}

	fmt.Println("Custom TOTP code verified successfully!")
	fmt.Println()

	// Example 3: Direct use of TOTPOptions
	fmt.Println("=== Example 3: Direct use of TOTPOptions ===")
	options := totp.TOTPOptions{
		Secret:      "JBSWY3DPEHPK3PXP", // Base32 encoded secret
		Period:      60,                 // 1 minute
		AccountName: "<EMAIL>",
		Issuer:      "DirectApp",
		Digits:      otp.DigitsSix,
		Algorithm:   otp.AlgorithmSHA1,
		Skew:        1,
	}

	// Generate a TOTP code directly
	directCode, err := totp.GenerateTOTPCode(options)
	if err != nil {
		log.Fatalf("Failed to generate TOTP directly: %v", err)
	}

	fmt.Printf("Generated TOTP code directly for %s: %s\n", options.AccountName, directCode)
	fmt.Println()

	// Verify the TOTP code directly
	valid, err := totp.VerifyTOTPCode(options, directCode)
	if err != nil {
		log.Fatalf("Failed to verify TOTP directly: %v", err)
	}

	if valid {
		fmt.Println("Direct TOTP code verified successfully!")
	} else {
		fmt.Println("Direct TOTP code verification failed!")
	}
}
