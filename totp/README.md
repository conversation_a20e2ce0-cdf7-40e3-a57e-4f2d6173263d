# TOTP Package

The TOTP (Time-based One-Time Password) package provides functionality for generating and validating time-based one-time passwords according to RFC 6238. This package is designed to be used for two-factor authentication (2FA) in applications.

## Features

- Generate TOTP codes with configurable digit count (6 or 8 digits)
- Configurable time periods (30 seconds, 5 minutes, etc.)
- Multiple hashing algorithm options (SHA1, SHA256, SHA512)
- Account-specific TOTP validation
- Redis-based storage for TOTP secrets
- One-time use TOTP codes (automatically invalidated after successful verification)

## Installation

```bash
go get github.com/liveutil/go-lib/totp
```

## Usage

### Basic Usage

```go
import (
    "github.com/pquerna/otp"
    "github.com/liveutil/go-lib/totp"
)

// Create TOTP options
options := totp.TOTPOptions{
    Secret:      "your-secret-key",
    Period:      300, // 5 minutes
    AccountName: "<EMAIL>",
    Issuer:      "YourApp",
    Digits:      otp.DigitsSix,
    Algorithm:   otp.AlgorithmSHA1,
    Skew:        1,
}

// Generate a TOTP code
code, err := totp.GenerateTOTPCode(options)
if err != nil {
    log.Fatalf("Failed to generate TOTP code: %v", err)
}

// Verify a TOTP code
valid, err := totp.VerifyTOTPCode(options, code)
if err != nil {
    log.Fatalf("Error verifying TOTP code: %v", err)
}
if valid {
    log.Println("TOTP code is valid")
} else {
    log.Println("TOTP code is invalid")
}
```

### Using the TOTP Service with Redis

```go
import (
    "context"
    "time"
    
    "github.com/pquerna/otp"
    "github.com/liveutil/go-lib/totp"
    "github.com/redis/go-redis/v9"
)

// Create a Redis client
redisClient := redis.NewClient(&redis.Options{
    Addr: "localhost:6379",
})
defer redisClient.Close()

// Create a TOTP service with default configuration
defaultConfig := totp.DefaultTOTPServiceConfig()
totpService := totp.NewRedisTOTPService(redisClient, "app", defaultConfig)

// Generate a TOTP code with 5 minutes expiration
ctx := context.Background()
code, err := totpService.GenerateTOTP(ctx, "<EMAIL>", 5*time.Minute)
if err != nil {
    log.Fatalf("Failed to generate TOTP: %v", err)
}

// Verify a TOTP code
err = totpService.VerifyTOTP(ctx, "<EMAIL>", code)
if err != nil {
    log.Fatalf("TOTP verification failed: %v", err)
}
```

### Custom Configuration

```go
// Create a custom TOTP service configuration
customConfig := totp.TOTPServiceConfig{
    Issuer:    "YourApp",
    Digits:    otp.DigitsEight,     // 8-digit codes
    Period:    30,                  // 30-second validity
    Algorithm: otp.AlgorithmSHA256, // Use SHA-256 for higher security
    Skew:      2,                   // Allow 2 periods before and after
}

// Create a TOTP service with custom configuration
totpService := totp.NewRedisTOTPService(redisClient, "app", customConfig)
```

## Configuration Options

### TOTPOptions

The `TOTPOptions` struct contains the configuration for TOTP generation and validation:

- `Secret`: The shared secret key used to generate and validate TOTP codes
- `Period`: The time step in seconds for TOTP code generation (default: 300 seconds / 5 minutes)
- `AccountName`: The identifier for the account (typically an email or username)
- `Issuer`: The name of the service or application issuing the TOTP
- `Digits`: The number of digits in the TOTP code (otp.DigitsSix or otp.DigitsEight)
- `Algorithm`: The hashing algorithm to use (otp.AlgorithmSHA1, otp.AlgorithmSHA256, or otp.AlgorithmSHA512)
- `Skew`: The number of periods before and after the current time to allow for validation

### TOTPServiceConfig

The `TOTPServiceConfig` struct provides default values for the TOTP service:

- `Issuer`: The default issuer name
- `Digits`: The default number of digits in the TOTP code
- `Period`: The default time step in seconds
- `Algorithm`: The default hashing algorithm
- `Skew`: The default number of periods to allow for validation

### RedisTOTPService

The `RedisTOTPService` provides a higher-level API for TOTP operations with Redis storage:

- `GenerateTOTP`: Generates a new TOTP code for the given account name with the specified expiration
- `VerifyTOTP`: Verifies a TOTP code for the given account name

## Error Handling

The package defines several error types:

- `ErrInvalidTOTP`: Returned when the TOTP code is invalid
- `ErrTOTPExpired`: Returned when the TOTP code has expired
- `ErrInvalidAccount`: Returned when the account name is invalid

## Security Considerations

- TOTP secrets should be stored securely and transmitted over secure channels
- The default hashing algorithm is SHA1, which is the standard for TOTP
- For higher security, SHA256 or SHA512 can be used
- The default period is 5 minutes, which provides a good balance between security and usability
- For stricter security, a shorter period (e.g., 30 seconds) can be used
- The default digit count is 6, but 8 digits provide higher security
- TOTP codes are one-time use and are automatically invalidated after successful verification

## Dependencies

- [github.com/pquerna/otp](https://github.com/pquerna/otp): Core TOTP implementation
- [github.com/redis/go-redis/v9](https://github.com/redis/go-redis): Redis client for Go

## License

This package is part of the Delsa Pay project and is subject to its licensing terms.
