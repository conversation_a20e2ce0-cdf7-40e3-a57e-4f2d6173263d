package grpcutil

import (
	"context"
	"encoding/json"
	"strconv"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
)

// Update getCachedUser to accept a context
func (a *authentication) getCachedUser(ctx context.Context, id string) (any, error) {
	// jaeger tracing
	ctx, span := a.tracer.Start(ctx, "authentication.getCachedUser")
	defer span.End()

	var user any

	err := a.redis.Get(ctx, id).Scan(&user)
	if err == nil {
		return user, nil
	}

	value, err := a.redis.Get(ctx, id).Result()
	if err != nil {
		span.RecordError(err, trace.WithAttributes(
			attribute.String("error", err.Error()),
			attribute.String("method", "authentication.getCachedUser.redis.Get"),
			attribute.String("user_id", id),
			attribute.String("message", "request validation failed"),
		))
		return user, err
	}

	err = json.Unmarshal([]byte(value), &user)
	if err != nil {
		span.RecordError(err, trace.WithAttributes(
			attribute.String("error", err.Error()),
			attribute.String("method", "authentication.getCachedUser.json.Unmarshal"),
			attribute.String("user_id", id),
			attribute.String("message", "request validation failed"),
		))
		return user, err
	}

	return user, nil
}

func (a *authentication) setUser(ctx context.Context, id int64, user any) {
	// jaeger tracing
	ctx, span := a.tracer.Start(ctx, "authentication.setUser")
	defer span.End()

	data, err := json.Marshal(user)
	if err != nil {
		span.RecordError(err, trace.WithAttributes(
			attribute.String("error", err.Error()),
			attribute.String("method", "authentication.setUser.json.Marshal"),
			attribute.String("user_id", strconv.FormatInt(id, 10)),
			attribute.String("message", "request validation failed"),
		))
	}

	err = a.redis.Set(ctx, strconv.FormatInt(id, 10), string(data), 0).Err()
	if err != nil {
		span.RecordError(err, trace.WithAttributes(
			attribute.String("error", err.Error()),
			attribute.String("method", "authentication.setUser.redis.Set"),
			attribute.String("user_id", strconv.FormatInt(id, 10)),
			attribute.String("message", "request validation failed"),
		))
	}
}
