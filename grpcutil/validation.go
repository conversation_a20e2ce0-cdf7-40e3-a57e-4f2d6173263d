package grpcutil

import (
	"context"
	"fmt"

	"github.com/liveutil/go-lib/jsonschema"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
	"google.golang.org/grpc"
)

type validation struct {
	schemas map[string][]byte
	tracer  trace.Tracer
}

func NewValidationInterceptor(schemaPath string, tracer trace.Tracer) (*validation, error) {
	schemas := make(map[string][]byte)
	err := jsonschema.LoadJsonValidationSchemas(schemaPath, schemas)
	if err != nil {
		return nil, err
	}

	return &validation{
		schemas: schemas,
		tracer:  tracer,
	}, nil
}

func (v *validation) ValidationInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req any, info *grpc.UnaryServerInfo, next grpc.UnaryHandler) (any, error) {
		ctx, span := v.tracer.Start(ctx, fmt.Sprintf("validate.%s", info.FullMethod))
		defer span.End()

		err := jsonschema.ValidateInput(req, v.schemas)
		if err != nil {
			span.RecordError(err, trace.WithAttributes(
				attribute.String("error", err.Error()),
				attribute.String("method", fmt.Sprintf("validate.%s", info.FullMethod)),
				attribute.String("message", "request validation failed"),
			))
			return nil, err
		}

		return next(ctx, req)
	}
}
