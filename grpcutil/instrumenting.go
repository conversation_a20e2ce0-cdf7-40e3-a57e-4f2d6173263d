package grpcutil

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kit/kit/metrics"
	"go.opentelemetry.io/otel/trace"
	"google.golang.org/grpc"
)

type instrumenting struct {
	requestCount   metrics.Counter
	requestLatency metrics.Histogram
	tracer         trace.Tracer
}

func NewInstrumentingInterceptor(counter metrics.Counter, latency metrics.Histogram, tracer trace.Tracer) *instrumenting {
	return &instrumenting{
		requestCount:   counter,
		requestLatency: latency,
		tracer:         tracer,
	}
}

func (i *instrumenting) InstrumentingInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req any, info *grpc.UnaryServerInfo, next grpc.UnaryHandler) (any, error) {
		ctx, span := i.tracer.Start(ctx, fmt.Sprintf("instrumenting.%s", info.FullMethod))
		defer span.End()

		defer func(begin time.Time, info *grpc.UnaryServerInfo) {
			i.requestCount.With("method", info.FullMethod).Add(1)
			i.requestLatency.With("method", info.FullMethod).Observe(time.Since(begin).Seconds())
		}(time.Now(), info)

		return next(ctx, req)
	}
}
