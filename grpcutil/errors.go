package grpcutil

import (
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// Define a custom error type for authentication failure
var (
	ErrUnauthenticated          = status.Error(codes.Unauthenticated, "authentication failed")
	ErrPermissionDenied         = status.Error(codes.PermissionDenied, "permission denied")
	ErrInternal                 = status.Error(codes.Internal, "internal server error")
	ErrCardIsExpired            = status.Error(codes.NotFound, "card is expired")
	ErrInvalidCardNumber        = status.Error(codes.NotFound, "provided card number is not invalid")
	ErrAccountBalanceExpired    = status.Error(codes.NotFound, "account balance expired")
	ErrNoRelatedAccountFound    = status.Error(codes.NotFound, "no related account found")
	ErrInsufficientBalance      = status.Error(codes.ResourceExhausted, "insufficient balance")
	ErrNoRelatedDepositFound    = status.Error(codes.NotFound, "no related deposit found")
	ErrDepositExpired           = status.Error(codes.NotFound, "deposit expired")
	ErrInvalidSigningKey        = status.Error(codes.PermissionDenied, "invalid signing key")
	ErrNoRelatedTerminalFound   = status.Error(codes.NotFound, "no related terminal found")
	ErrCustomerNotFound         = status.Error(codes.NotFound, "customer not found")
	ErrServiceShutdown          = status.Error(codes.Aborted, "service shutdown")
	ErrCardlessTransferNotFound = status.Error(codes.NotFound, "cardless transfer not found or expired")
	ErrInvalidTOTP              = status.Error(codes.NotFound, "provided TOTP is not valid or expired")
	ErrRateLimitExceeded        = status.Error(codes.ResourceExhausted, "rate limit exceeded")
)

// grpcError returns a custom error type for grpc response errors.
func GrpcError(code codes.Code, message string) error {
	return status.Error(code, message)
}
