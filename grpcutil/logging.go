package grpcutil

import (
	"context"
	"fmt"
	"time"

	kitlog "github.com/go-kit/log"
	"github.com/liveutil/go-lib/contextutil"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
	"google.golang.org/grpc"
)

type logging struct {
	logger kitlog.Logger
	tracer trace.Tracer
}

func NewLoggingInterceptor(logger kitlog.Logger, tracer trace.Tracer) *logging {
	return &logging{
		logger: logger,
		tracer: tracer,
	}
}

func (l *logging) LoggingInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, next grpc.UnaryHandler) (any, error) {
		ctx, span := l.tracer.Start(ctx, fmt.Sprintf("logging.%s", info.FullMethod))
		defer span.End()

		res, err := next(ctx, req)
		if err != nil {
			span.RecordError(err, trace.WithAttributes(
				attribute.String("error", err.Error()),
				attribute.String("method", info.FullMethod),
				attribute.String("input", fmt.Sprintf("%v", req)),
				attribute.String("response", fmt.Sprintf("%v", res)),
			))
		}

		defer func(begin time.Time, req interface{}, res any, err error) {
			_ = l.logger.Log(
				"method", info.FullMethod,
				"context_user_id", ctx.Value(contextutil.ContextUserIDKey),
				"client_ip", ctx.Value(contextutil.ContextRemoteIPKey),
				"error", err,
				"took", time.Since(begin),
				"input", req,
				"response", res,
			)
		}(time.Now(), req, res, err)

		return res, err
	}
}
