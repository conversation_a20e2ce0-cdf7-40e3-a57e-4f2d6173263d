package grpcutil

import (
	"context"
	"fmt"
	"net"
	"strconv"
	"strings"
	"time"

	"github.com/liveutil/go-lib/contextutil"
	"github.com/liveutil/go-lib/paseto"
	"github.com/redis/go-redis/v9"
	"github.com/thoas/go-funk"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/peer"
)

type (
	GetSafeUserInfo func(ctx context.Context, id int64) (interface{}, error)

	authentication struct {
		redis       *redis.Client
		options     AuthenticationOptions
		tracer      trace.Tracer
		accessRoles map[string][]string
	}

	AuthenticationOptions struct {
		HealthCheckKey string
		Secret         string
		GetUserInfo    GetSafeUserInfo
		Paseto         paseto.Maker
		AccessRoles    map[string][]string
		Tracer         trace.Tracer
	}
)

func NewPasetoAuthInterceptor(redis *redis.Client, options AuthenticationOptions) *authentication {
	return &authentication{
		redis:       redis,
		options:     options,
		tracer:      options.Tracer,
		accessRoles: options.AccessRoles,
	}
}

func (a *authentication) PasetoAuthInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req any, info *grpc.UnaryServerInfo, next grpc.UnaryHandler) (any, error) {
		ctx, span := a.tracer.Start(ctx, fmt.Sprintf("authentication.%s", info.FullMethod))
		defer span.End()

		// Get peer information from context
		peerInfo, ok := peer.FromContext(ctx)
		if !ok {
			span.RecordError(ErrUnauthenticated, trace.WithAttributes(
				attribute.String("error", ErrUnauthenticated.Error()),
				attribute.String("method", fmt.Sprintf("authentication.%s.peer.FromContext", info.FullMethod)),
				attribute.String("message", "peer info not found in context"),
			))
			return nil, ErrUnauthenticated
		}

		// Extract metadata from the request
		contextMetadata, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			span.RecordError(ErrUnauthenticated, trace.WithAttributes(
				attribute.String("error", ErrUnauthenticated.Error()),
				attribute.String("method", fmt.Sprintf("authentication.%s.metadata.FromIncomingContext", info.FullMethod)),
				attribute.String("message", "peer info not found in context"),
			))
			return nil, ErrUnauthenticated
		}

		// example value ("/user_service.UserService/SignUp")
		method := info.FullMethod
		// Extract just the method name from the full path
		methodParts := strings.Split(method, "/")
		methodName := methodParts[len(methodParts)-1]

		// check if method has access expectedRoles
		var expectedRoles []string

		if definedRoles, ok := a.accessRoles[methodName]; ok {
			if len(definedRoles) == 0 {
				return next(ctx, req)
			}
			expectedRoles = definedRoles
		} else {
			return next(ctx, req)
		}

		// Get the PASETO token from the authorization header
		auth, ok := contextMetadata["authorization"]
		if !ok || len(auth) == 0 {
			span.RecordError(ErrUnauthenticated, trace.WithAttributes(
				attribute.String("error", ErrUnauthenticated.Error()),
				attribute.String("method", fmt.Sprintf("authentication.%s.authorizationHeader", info.FullMethod)),
				attribute.String("message", "authorization header not found in metadata"),
			))
			return nil, ErrUnauthenticated
		}

		// Split the authorization header to get the token
		ah := strings.Split(auth[0], " ")

		// abort request if Authenticate header is empty or not provided.
		if len(ah) != 2 {
			span.RecordError(ErrUnauthenticated, trace.WithAttributes(
				attribute.String("error", ErrUnauthenticated.Error()),
				attribute.String("method", fmt.Sprintf("authentication.%s.authorizationHeader", info.FullMethod)),
				attribute.String("message", "authorization header is not properly formatted, it should be 'Bearer <token>'"),
			))
			return nil, ErrUnauthenticated
		}

		// Check if the token is present and properly formatted
		if strings.ToLower(ah[0]) != "bearer" {
			span.RecordError(ErrUnauthenticated, trace.WithAttributes(
				attribute.String("error", ErrUnauthenticated.Error()),
				attribute.String("method", fmt.Sprintf("authentication.%s.authorizationHeader", info.FullMethod)),
				attribute.String("message", "authorization header is not properly formatted, it should be 'Bearer <token>'"),
			))
			return nil, ErrUnauthenticated
		}

		// capture token claims from provided token
		claims, err := a.options.Paseto.VerifyToken(ah[1])
		if err != nil {
			span.RecordError(ErrUnauthenticated, trace.WithAttributes(
				attribute.String("error", ErrUnauthenticated.Error()),
				attribute.String("method", fmt.Sprintf("authentication.%s.verifyToken", info.FullMethod)),
				attribute.String("message", "failed to verify PASETO token"),
			))
			return nil, ErrUnauthenticated
		}

		// check if token is expired
		if claims.ExpiresAt.Before(time.Now().UTC()) {
			span.RecordError(ErrUnauthenticated, trace.WithAttributes(
				attribute.String("error", ErrUnauthenticated.Error()),
				attribute.String("method", fmt.Sprintf("authentication.%s.tokenExpired", info.FullMethod)),
				attribute.String("message", "PASETO token is expired"),
			))
			return nil, ErrUnauthenticated
		}

		// if len(a.options.Roles) > 0 {
		if len(funk.IntersectString(expectedRoles, claims.Roles)) == 0 {
			span.RecordError(ErrPermissionDenied, trace.WithAttributes(
				attribute.String("error", ErrPermissionDenied.Error()),
				attribute.String("method", fmt.Sprintf("authentication.%s.permissionDenied", info.FullMethod)),
				attribute.String("message", "user does not have required roles"),
			))
			return nil, ErrPermissionDenied
		}
		// }

		// add services.UserDTO to context
		ctx, err = a.user(ctx, claims.Subject)
		if err != nil {
			span.RecordError(ErrInternal, trace.WithAttributes(
				attribute.String("error", ErrInternal.Error()),
				attribute.String("method", fmt.Sprintf("authentication.%s.user", info.FullMethod)),
				attribute.String("message", "failed to get user from context or cached redis db"),
			))
			return nil, ErrInternal
		}

		ctx = context.WithValue(ctx, contextutil.ContextClaimsKey, claims)
		ctx = context.WithValue(ctx, contextutil.ContextRoleKey, claims.Roles)
		ctx = context.WithValue(ctx, contextutil.ContextUserIDKey, claims.Subject)
		ctx = context.WithValue(ctx, contextutil.ContextRemoteIPKey, peerInfo.Addr.(*net.TCPAddr).IP.String())

		// Call the handler function
		return next(ctx, req)
	}
}

// user reads services.UserDTO from Redis or Database and sets it on context
func (a *authentication) user(ctx context.Context, id string) (context.Context, error) {
	// jaeger tracing
	ctx, span := a.tracer.Start(ctx, "authentication.user")
	defer span.End()

	// Create a context with timeout for Redis operations
	redisCtx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()

	// get user from redis
	user, err := a.getCachedUser(redisCtx, id)
	if err != nil {
		// get user from database
		uid, err := strconv.ParseInt(id, 10, 64)
		if err != nil {
			return nil, err
		}

		user, err := a.options.GetUserInfo(ctx, uid)
		if err != nil {
			return nil, err
		}

		// cache user on redis in a goroutine to not block the request
		go func() {
			a.setUser(ctx, uid, user)
		}()

		return context.WithValue(ctx, contextutil.ContextUserKey, user), nil
	}

	// set user on context
	return context.WithValue(ctx, contextutil.ContextUserKey, user), nil
}

func CatchUser(ctx context.Context) (interface{}, error) {
	value := ctx.Value(contextutil.ContextUserKey)
	if value == nil {
		return nil, ErrUnauthenticated
	}
	return value, nil
}

func CatchClaims(ctx context.Context) (claims paseto.TokenClaims, err error) {
	value, ok := ctx.Value(contextutil.ContextClaimsKey).(paseto.TokenClaims)
	if !ok {
		return claims, ErrUnauthenticated
	}
	return value, nil
}
