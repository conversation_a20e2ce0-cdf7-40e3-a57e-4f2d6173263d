package servicemesh

import (
	"context"
	"encoding/json"

	"github.com/nats-io/nats.go"
)

// NewNatsCardMeshServiceClient creates a new CardMeshService client using NATS
// natsConn is the NATS connection to use for communication
func NewNatsCardMeshServiceClient(natsConn *nats.Conn) CardMeshService {
	return &natsCardMeshService{
		nats: natsConn,
	}
}

// natsCardMeshService implements the MeshService interface using NATS for communication
type natsCardMeshService struct {
	nats *nats.Conn
}

// GenerateDefaultCardForUser implements CardMeshService.
func (n *natsCardMeshService) GenerateDefaultCardForUser(ctx context.Context, message CardMeshServiceMessage) (*CardModel, error) {
	jsonMessage, err := json.Marshal(message)
	if err != nil {
		return nil, err
	}

	msg, err := n.nats.Request(CARD_SERVICE_GENERATE_DEFAULT_CARD_FOR_USER, jsonMessage, nats.DefaultDrainTimeout)
	if err != nil {
		return nil, err
	}

	var cardInfo CardModel
	if err := json.Unmarshal(msg.Data, &cardInfo); err != nil {
		return nil, err
	}

	return &cardInfo, nil
}

// GetSafeCardsByUserIdentifier implements CardMeshService.
func (n *natsCardMeshService) GetSafeCardsByUserIdentifier(ctx context.Context, userIdentifier string) ([]*CardModel, error) {
	message := CardMeshServiceMessage{
		UserIdentifier: userIdentifier,
	}

	jsonMessage, err := json.Marshal(message)
	if err != nil {
		return nil, err
	}

	msg, err := n.nats.Request(CARD_SERVICE_GET_SAFE_CARDS_BY_USER_IDENTIFIER, jsonMessage, nats.DefaultDrainTimeout)
	if err != nil {
		return nil, err
	}

	var cardInfo []*CardModel
	if err := json.Unmarshal(msg.Data, &cardInfo); err != nil {
		return nil, err
	}

	return cardInfo, nil
}

// GetSafeCardTypeByTypeCode implements CardMeshService.
func (n *natsCardMeshService) GetSafeCardTypeByTypeCode(ctx context.Context, typeCode string) (*CardTypeModel, error) {
	message := CardMeshServiceMessage{
		CardTypeCode: typeCode,
	}

	jsonMessage, err := json.Marshal(message)
	if err != nil {
		return nil, err
	}

	msg, err := n.nats.Request(CARD_SERVICE_GET_SAFE_CARD_TYPE_BY_TYPE_CODE, jsonMessage, nats.DefaultDrainTimeout)
	if err != nil {
		return nil, err
	}

	var cardType CardTypeModel
	if err := json.Unmarshal(msg.Data, &cardType); err != nil {
		return nil, err
	}

	return &cardType, nil
}

// GetSafeCardByCardNumber implements MeshService.
func (n *natsCardMeshService) GetSafeCardByCardNumber(ctx context.Context, cardNumber string) (*CardModel, error) {
	message := CardMeshServiceMessage{
		CardNumber: cardNumber,
	}

	jsonMessage, err := json.Marshal(message)
	if err != nil {
		return nil, err
	}

	msg, err := n.nats.Request(CARD_SERVICE_GET_SAFE_CARD_BY_CARD_NUMBER, jsonMessage, nats.DefaultDrainTimeout)
	if err != nil {
		return nil, err
	}

	var cardInfo CardModel
	if err := json.Unmarshal(msg.Data, &cardInfo); err != nil {
		return nil, err
	}

	return &cardInfo, nil
}

// GetSafeIssuerByIdentifier implements MeshService.
func (n *natsCardMeshService) GetSafeIssuerByIdentifier(ctx context.Context, identifier string) (*IssuerIdentifier, error) {
	message := CardMeshServiceMessage{
		IssuerIdentifier: identifier,
	}

	jsonMessage, err := json.Marshal(message)
	if err != nil {
		return nil, err
	}

	msg, err := n.nats.Request(CARD_SERVICE_GET_SAFE_ISSUER_IDENTIFIER_BY_IDENTIFIER, jsonMessage, nats.DefaultDrainTimeout)
	if err != nil {
		return nil, err
	}

	var issuerInfo IssuerIdentifier
	if err := json.Unmarshal(msg.Data, &issuerInfo); err != nil {
		return nil, err
	}

	return &issuerInfo, nil
}

// GetSafeIssuerByIIN implements MeshService.
func (n *natsCardMeshService) GetSafeIssuerByIIN(ctx context.Context, iin string) (*IssuerIdentifier, error) {
	message := CardMeshServiceMessage{
		CardTypeCode: iin,
	}

	jsonMessage, err := json.Marshal(message)
	if err != nil {
		return nil, err
	}

	msg, err := n.nats.Request(CARD_SERVICE_GET_SAFE_ISSUER_IDENTIFIER_BY_IIN, jsonMessage, nats.DefaultDrainTimeout)
	if err != nil {
		return nil, err
	}

	var issuerInfo IssuerIdentifier
	if err := json.Unmarshal(msg.Data, &issuerInfo); err != nil {
		return nil, err
	}

	return &issuerInfo, nil
}

// Start implements CardMeshService.
func (n *natsCardMeshService) Start(ctx context.Context, subjects []string) error {
	return nil
}

// Stop implements CardMeshService.
func (n *natsCardMeshService) Stop(ctx context.Context) error {
	return nil
}
