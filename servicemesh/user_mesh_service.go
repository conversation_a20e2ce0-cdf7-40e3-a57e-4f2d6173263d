package servicemesh

import (
	"context"
	"time"
)

const (
	USER_SERVICE_GET_SAFE_USER_BY_ID         = "user_service.GetSafeUserByID"
	USER_SERVICE_GET_SAFE_USER_BY_IDENTIFIER = "user_service.GetSafeUserByIdentifier"
)

// UserMeshServiceMessage is the message structure for user mesh service operations
type UserMeshServiceMessage struct {
	// UserID is the ID of the user to retrieve
	UserID int64 `json:"user_id"`
	// Identifier is the external system identifier of the user to retrieve
	Identifier string `json:"identifier"`
}

// UserMeshService defines the interface for service mesh operations
type UserMeshService interface {
	// GetSafeUserByID retrieves a safe user by their ID
	GetSafeUserByID(ctx context.Context, userID int64) (*UserModel, error)
	// GetSafeUserByIdentifier retrieves a safe user by their external system identifier
	GetSafeUserByIdentifier(ctx context.Context, identifier string) (*UserModel, error)

	// Start starts the service mesh
	Start(ctx context.Context, subjects []string) error
	// Stop stops the service mesh
	Stop(ctx context.Context) error
}

// UserModel is a concrete implementation of SafeUserInfo
type UserModel struct {
	// user unique id
	ID int64 `json:"id"`
	// unique external identifier for inter system internal-external identifier separation
	Identifier string `json:"identifier"`
	// is user approved or no
	Approved bool `json:"approved"`
	// is user banned or no
	Banned bool `json:"banned"`
	// user meta data
	MetaData []byte `json:"meta_data"`
	// user assigned roles for permission controls
	Roles []string `json:"roles"`
	// expire time of user, if not sets then user valid for unlimited time
	ExpiresAt *time.Time `json:"expires_at"`
	// when user was created
	CreatedAt time.Time `json:"created_at"`
	// when user was updated
	UpdatedAt *time.Time `json:"updated_at"`
	// when user was deleted
	DeletedAt *time.Time `json:"deleted_at"`
	// related user profile
	Profile ProfileModel `json:"profile"`
	// related user contact
	Contact ContactModel `json:"contact"`
}

type ContactModel struct {
	// contact unique id
	ID int64 `json:"id"`
	// unique external identifier for inter system internal-external identifier separation
	Identifier string `json:"identifier"`
	// contact primary type
	ContactType string `json:"contact_type"`
	// related user id to determining session owner account
	UserID int64 `json:"user_id"`
	// contact primary mobile phone number for authorization use
	Mobile string `json:"mobile"`
	// holds TOTP bcrypt pass code
	MobileTotp string `json:"mobile_totp"`
	// sets to true if user verified his mobile by first time otp verification
	IsMobileVerified bool `json:"is_mobile_verified"`
	// holds by mobile OTP verification code expire time
	MobileTotpExpiresAt *time.Time `json:"mobile_totp_expires_at"`
	// contact primary e-mail address
	Email string `json:"email"`
	// holds TOTP bcrypt pass code
	EmailTotp string `json:"email_totp"`
	// sets to true if user verified his email by first time otp verification
	IsEmailVerified bool `json:"is_email_verified"`
	// holds by e-mail OTP verification code expire time
	EmailTotpExpiresAt *time.Time `json:"email_totp_expires_at"`
	// contact metadata
	MetaData []byte `json:"meta_data"`
	// when contact was created
	CreatedAt time.Time `json:"created_at"`
	// when contact was updated
	UpdatedAt *time.Time `json:"updated_at"`
	// when contact was deleted
	DeletedAt *time.Time `json:"deleted_at"`
}

type ProfileModel struct {
	// profile unique id
	ID int64 `json:"id"`
	// unique external identifier for inter system internal-external identifier separation
	Identifier string `json:"identifier"`
	// related user id to determining session owner account
	UserID int64 `json:"user_id"`
	// legal or natural person type deffination
	ProfileType string `json:"profile_type"`
	// user first name
	FirstName string `json:"first_name"`
	// user last name
	LastName string `json:"last_name"`
	// user unique personal national id-code
	NationalID string `json:"national_id"`
	// profile control status
	Status string `json:"status"`
	// profile metadata
	MetaData []byte `json:"meta_data"`
	// when profile was created
	CreatedAt time.Time `json:"created_at"`
	// when profile was updated
	UpdatedAt *time.Time `json:"updated_at"`
	// when profile was deleted
	DeletedAt *time.Time `json:"deleted_at"`
}
