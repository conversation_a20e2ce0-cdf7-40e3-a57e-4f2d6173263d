package servicemesh

import (
	"context"
	"time"
)

const (
	CARD_SERVICE_GET_SAFE_CARD_BY_CARD_NUMBER             = "card_service.GetSafeCardByCardNumber"
	CARD_SERVICE_GET_SAFE_CARDS_BY_USER_IDENTIFIER        = "card_service.GetSafeCardsByUserIdentifier"
	CARD_SERVICE_GET_SAFE_CARD_TYPE_BY_TYPE_CODE          = "card_service.GetSafeCardTypeByTypeCode"
	CARD_SERVICE_GET_SAFE_ISSUER_IDENTIFIER_BY_IIN        = "card_service.GetSafeIssuerByIIN"
	CARD_SERVICE_GET_SAFE_ISSUER_IDENTIFIER_BY_IDENTIFIER = "card_service.GetSafeIssuerByIdentifier"
	CARD_SERVICE_GENERATE_DEFAULT_CARD_FOR_USER           = "card_service.GenerateDefaultCardForUser"
)

// CardMeshServiceMessage is the message structure for card mesh service operations
type CardMeshServiceMessage struct {
	// CardNumber is the number of the card to retrieve
	CardNumber string `json:"card_number"`
	// IssuerIdentifier is the external system identifier of the card issuer to retrieve
	IssuerIdentifier string `json:"issuer_identifier"`
	// CardTypeCode is the code of the card type to retrieve
	CardTypeCode string `json:"card_type_code"`
	// UserIdentifier is the external system identifier of the card user to retrieve
	UserIdentifier string `json:"user_identifier"`
}

// CardMeshService defines the interface for service mesh operations
type CardMeshService interface {
	// GenerateDefaultCardForUser
	GenerateDefaultCardForUser(ctx context.Context, message CardMeshServiceMessage) (*CardModel, error)
	// GetSafeCardByCardNumber retrieves a safe card by their number
	GetSafeCardByCardNumber(ctx context.Context, cardNumber string) (*CardModel, error)
	// GetSafeCardsByUserIdentifier retrieves a safe cards by their user identifier
	GetSafeCardsByUserIdentifier(ctx context.Context, userIdentifier string) ([]*CardModel, error)
	// GetSafeIssuerByIdentifier retrieves a safe issuer identifier by their external identifier
	GetSafeIssuerByIdentifier(ctx context.Context, identifier string) (*IssuerIdentifier, error)
	// GetSafeIssuerByIIN retrieves a safe issuer identifier by their code
	GetSafeIssuerByIIN(ctx context.Context, iin string) (*IssuerIdentifier, error)
	// GetSafeCardTypeByTypeCode retrieves a safe card type by their external identifier
	GetSafeCardTypeByTypeCode(ctx context.Context, typeCode string) (*CardTypeModel, error)
	// Start starts the service mesh
	Start(ctx context.Context, subjects []string) error
	// Stop stops the service mesh
	Stop(ctx context.Context) error
}

// CardModel is a concrete implementation of SafeCardInfo
type CardModel struct {
	// unique external identifier for internal-external system identifier isolation
	Identifier string `json:"identifier"`
	// owner of card external identifier
	UserIdentifier string `json:"user_identifier"`
	// card status determinable value
	Status string `json:"status"`
	// card issuer identifier id
	Iin int64 `json:"iin"`
	// card type id
	CardType int64 `json:"card_type"`
	// card issuer identifier code number
	IinCode string `json:"iin_code"`
	// card / service or issuer defined card type code
	CardTypeCode string `json:"card_type_code"`
	// card owner account number
	AccountNumber string `json:"account_number"`
	// Luhn Checksum digit for given (iin + account_number) string
	LuhnDigit string `json:"luhn_digit"`
	// bcrypted CVV2 generated 4-digit using HMAC-SHA256
	Cvv string `json:"cvv"`
	// bcrypted random unique 4 or 6 digit pin code number that used for signing transactions
	PinCode string `json:"pin_code"`
	// complete card number string using [iin(4) + card_type_code(2) + account_number(10) + luhn_checksum(1) = 16 character
	CardNumber string `json:"card_number"`
	// issued card valid before this year
	ExpireYear string `json:"expire_year"`
	// issued card valid before this year / month
	ExpireMoth string `json:"expire_moth"`
	// card metadatas
	MetaData []byte `json:"meta_data"`
	// when the card will expires
	ExpiresAt *time.Time `json:"expires_at"`
	// when card was created
	CreatedAt time.Time `json:"created_at"`
	// when card was updated
	UpdatedAt *time.Time `json:"updated_at"`
	// when card was deleted
	DeletedAt *time.Time `json:"deleted_at"`
}

// CardTypeModel is a concrete implementation of SafeCardTypeInfo
type CardTypeModel struct {
	// unique external identifier for internal-external system identifier isolation
	Identifier string `json:"identifier"`
	// card type code
	Code string `json:"code"`
	// card type name
	Name string `json:"name"`
	// card type description
	Description string `json:"description"`
	// card type metadatas
	MetaData []byte `json:"meta_data"`
	// card issuer identifier id
	Iin int64 `json:"iin"`
	// when card type was created
	CreatedAt time.Time `json:"created_at"`
	// when card type was updated
	UpdatedAt *time.Time `json:"updated_at"`
	// when card type was deleted
	DeletedAt *time.Time `json:"deleted_at"`
}

// IssuerIdentifier is a concrete implementation of SafeIssuerInfo
type IssuerIdentifier struct {
	// unique external identifier for internal-external system identifier isolation
	Identifier string `json:"identifier"`
	// card issuer identifier number
	Iin string `json:"iin"`
	// iin registry status determinable value
	Status string `json:"status"`
	// issuer owner user external identifier
	IssuerUserIdentifier string `json:"issuer_user_identifier"`
	// issuer name
	IssuerName string `json:"issuer_name"`
	// issuer logo url
	IssuerLogo string `json:"issuer_logo"`
	// issuer website url
	IssuerUrl string `json:"issuer_url"`
	// iin registry metadatas
	MetaData []byte `json:"meta_data"`
	// when the iin registry will expires
	ExpiresAt *time.Time `json:"expires_at"`
	// when iin registry was created
	CreatedAt time.Time `json:"created_at"`
	// when iin registry was updated
	UpdatedAt *time.Time `json:"updated_at"`
	// when iin registry was deleted
	DeletedAt *time.Time `json:"deleted_at"`
}
