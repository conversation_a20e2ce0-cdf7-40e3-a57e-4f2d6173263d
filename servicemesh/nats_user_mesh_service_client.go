package servicemesh

import (
	"context"
	"encoding/json"

	"github.com/nats-io/nats.go"
)

// NewNatsUserMeshServiceClient creates a new UserMeshService client using NATS
// natsConn is the NATS connection to use for communication
func NewNatsUserMeshServiceClient(natsConn *nats.Conn) UserMeshService {
	return &natsUserMeshService{
		nats: natsConn,
	}
}

// natsUserMeshService implements the MeshService interface using NATS for communication
type natsUserMeshService struct {
	nats *nats.Conn
}

// Start implements UserMeshService.
func (n *natsUserMeshService) Start(ctx context.Context, subjects []string) error {
	return nil
}

// Stop implements UserMeshService.
func (n *natsUserMeshService) Stop(ctx context.Context) error {
	return nil
}

// GetSafeUserByID implements MeshService.
func (n *natsUserMeshService) GetSafeUserByID(ctx context.Context, userID int64) (*UserModel, error) {
	message := UserMeshServiceMessage{
		UserID: userID,
	}

	jsonMessage, err := json.Marshal(message)
	if err != nil {
		return nil, err
	}

	msg, err := n.nats.Request(USER_SERVICE_GET_SAFE_USER_BY_ID, jsonMessage, nats.DefaultDrainTimeout)
	if err != nil {
		return nil, err
	}

	var userInfo UserModel
	if err := json.Unmarshal(msg.Data, &userInfo); err != nil {
		return nil, err
	}

	return &userInfo, nil
}

// GetSafeUserByIdentifier implements MeshService.
func (n *natsUserMeshService) GetSafeUserByIdentifier(ctx context.Context, identifier string) (*UserModel, error) {
	message := UserMeshServiceMessage{
		Identifier: identifier,
	}

	jsonMessage, err := json.Marshal(message)
	if err != nil {
		return nil, err
	}

	msg, err := n.nats.Request(USER_SERVICE_GET_SAFE_USER_BY_IDENTIFIER, jsonMessage, nats.DefaultDrainTimeout)
	if err != nil {
		return nil, err
	}

	var userInfo UserModel
	if err := json.Unmarshal(msg.Data, &userInfo); err != nil {
		return nil, err
	}

	return &userInfo, nil
}
