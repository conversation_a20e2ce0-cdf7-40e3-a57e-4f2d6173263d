package worker

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/hibiken/asynq"
)

const (
	TaskSendVerifyEmail   = "task:send_verify_email"
	TaskSendVerifyMessage = "task:send_verify_sms"
)

type PayloadSendVerificationCode struct {
	Username         string `json:"username"`
	Email            string `json:"email"`
	Mobile           string `json:"mobile"`
	VerificationCode string `json:"verification_code"`
}

func (d *RedisTaskDistributor) DistributeTaskSendVerificationCode(
	ctx context.Context,
	payload *PayloadSendVerificationCode,
	opts ...asynq.Option,
) error {
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal task payload: %w", err)
	}

	if len(payload.Email) > 0 {
		task := asynq.NewTask(TaskSendVerifyEmail, jsonPayload, opts...)
		info, err := d.client.EnqueueContext(ctx, task)
		if err != nil {
			return fmt.Errorf("failed to enqueue task: %w", err)
		}

		_ = d.logger.Log(
			"type", task.Type(),
			"payload", string(task.Payload()),
			"queue", info.Queue,
			"max_retry", info.MaxRetry,
			"status", "enqueued email task",
		)
	} else {
		task := asynq.NewTask(TaskSendVerifyMessage, jsonPayload, opts...)
		info, err := d.client.EnqueueContext(ctx, task)
		if err != nil {
			return fmt.Errorf("failed to enqueue task: %w", err)
		}

		_ = d.logger.Log(
			"type", task.Type(),
			"payload", string(task.Payload()),
			"queue", info.Queue,
			"max_retry", info.MaxRetry,
			"status", "enqueued sms task",
		)
	}

	return nil
}

func (processor *RedisTaskProcessor) ProcessTaskSendVerifyEmail(ctx context.Context, task *asynq.Task) error {
	_ = processor.logger.Log(
		"type", task.Type(),
		"payload", task.Payload(),
		"status", "sent",
	)
	return nil
}

func (processor *RedisTaskProcessor) ProcessTaskSendVerifyMessage(ctx context.Context, task *asynq.Task) error {
	_ = processor.logger.Log(
		"type", task.Type(),
		"payload", task.Payload(),
		"status", "sent",
	)
	return nil
}
