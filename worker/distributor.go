package worker

import (
	"context"

	kitlog "github.com/go-kit/log"

	"github.com/hibiken/asynq"
)

type TaskDistributor interface {
	DistributeTaskSendVerificationCode(
		ctx context.Context,
		payload *PayloadSendVerificationCode,
		opts ...asynq.Option,
	) error

	DistributeTaskMinioBackup(
		ctx context.Context,
		payload *PayloadBackupMinio,
		opts ...asynq.Option,
	) error
}

type RedisTaskDistributor struct {
	client *asynq.Client
	logger kitlog.Logger
}

func NewRedisTaskDistributor(redisOpt asynq.RedisClientOpt, logger kitlog.Logger) TaskDistributor {
	client := asynq.NewClient(redisOpt)
	return &RedisTaskDistributor{
		client: client,
		logger: logger,
	}
}
