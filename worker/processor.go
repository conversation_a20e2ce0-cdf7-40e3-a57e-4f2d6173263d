package worker

import (
	"context"
	"time"

	kitlog "github.com/go-kit/log"
	pl "github.com/liveutil/go-lib/logger"
	"github.com/liveutil/go-lib/mailer"

	"github.com/go-redis/redis/v8"
	"github.com/hibiken/asynq"
)

const (
	QueueCritical = "critical"
	QueueDefault  = "default"
)

type TaskProcessor interface {
	Start() error
	ProcessTaskSendVerifyEmail(ctx context.Context, task *asynq.Task) error
	ProcessTaskSendVerifyMessage(ctx context.Context, task *asynq.Task) error
	ProcessTaskBackupMinio(ctx context.Context, task *asynq.Task) error
}

type RedisTaskProcessor struct {
	server *asynq.Server
	mailer mailer.EmailSender
	logger kitlog.Logger
}

const (
	// Concurrency defines how many workers can process tasks concurrently
	ConcurrencyCritical = 10
	ConcurrencyDefault  = 20

	// RetryLimit defines how many times a failed task should be retried
	RetryLimit = 3

	// ProcessTimeout defines the maximum time a task can run before timing out
	ProcessTimeout = 5 * time.Minute
)

func NewRedisTaskProcessor(redisOpt asynq.RedisClientOpt, logger kitlog.Logger, mailer mailer.EmailSender) TaskProcessor {

	redisLogger := pl.NewLogger()
	redis.SetLogger(redisLogger)

	server := asynq.NewServer(
		redisOpt,
		asynq.Config{
			Queues: map[string]int{
				QueueCritical: 10,
				QueueDefault:  5,
			},
			// Set concurrency for each queue
			Concurrency: ConcurrencyCritical + ConcurrencyDefault,
			// Retry failed tasks with exponential backoff
			RetryDelayFunc: func(n int, err error, task *asynq.Task) time.Duration {
				return time.Duration(n*n) * time.Second
			},
			ErrorHandler: asynq.ErrorHandlerFunc(func(ctx context.Context, task *asynq.Task, err error) {
				_ = logger.Log(
					"type", task.Type(),
					"payload", task.Payload(),
					"error", err.Error(),
					"status", "failed",
				)
			}),
			Logger: redisLogger,
			// Set shutdown timeout
			ShutdownTimeout: ProcessTimeout,
		},
	)

	return &RedisTaskProcessor{
		server: server,
		mailer: mailer,
		logger: logger,
	}
}

func (processor *RedisTaskProcessor) Start() error {
	mux := asynq.NewServeMux()

	// Register task handlers
	mux.HandleFunc(TaskSendVerifyEmail, processor.ProcessTaskSendVerifyEmail)
	mux.HandleFunc(TaskSendVerifyMessage, processor.ProcessTaskSendVerifyMessage)
	mux.HandleFunc(TaskBackupMinio, processor.ProcessTaskBackupMinio)

	// Log server start
	_ = processor.logger.Log(
		"msg", "Starting task processor server",
		"queues", "critical, default",
	)

	return processor.server.Start(mux)
}
