package worker

import (
	"context"
	"encoding/json"
	"fmt"
	"io/fs"
	"os"

	"github.com/hibiken/asynq"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

const (
	TaskBackupMinio = "task:backup_minio"
)

type PayloadBackupMinio struct {
	SkipErrors      bool   `json:"skip_errors"`
	BackupDirectory string `json:"backup_directory"`
	Endpoint        string `json:"endpoint"`
	AccessKeyID     string `json:"access_key_id"`
	SecretAccessKey string `json:"secret_access_key"`
}

func (d *RedisTaskDistributor) DistributeTaskMinioBackup(
	ctx context.Context,
	payload *PayloadBackupMinio,
	opts ...asynq.Option,
) error {
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal task payload: %w", err)
	}

	task := asynq.NewTask(TaskBackupMinio, jsonPayload, opts...)
	info, err := d.client.EnqueueContext(ctx, task)
	if err != nil {
		return fmt.Errorf("failed to enqueue task: %w", err)
	}

	_ = d.logger.Log(
		"type", task.Type(),
		"payload", string(task.Payload()),
		"queue", info.Queue,
		"max_retry", info.MaxRetry,
		"status", "enqueued minio backup task",
	)

	return nil
}

func (processor *RedisTaskProcessor) ProcessTaskBackupMinio(ctx context.Context, task *asynq.Task) error {
	payload := &PayloadBackupMinio{}

	if err := json.Unmarshal(task.Payload(), payload); err != nil {
		_ = processor.logger.Log(
			"type", task.Type(),
			"payload", task.Payload(),
			"status", fmt.Errorf("failed to unmarshal task payload: %w", err),
		)
		return err
	}

	// Initialize minio client object.
	minioClient, err := minio.New(payload.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(payload.AccessKeyID, payload.SecretAccessKey, ""),
		Secure: false, // Set this value according to your Minio server setup.
	})

	if err != nil {
		_ = processor.logger.Log(
			"type", task.Type(),
			"payload", task.Payload(),
			"status", fmt.Errorf("failed connect to minio: %w", err),
		)
		return err
	}

	// List all buckets on the Minio server.
	buckets, err := minioClient.ListBuckets(ctx)
	if err != nil {
		_ = processor.logger.Log(
			"type", task.Type(),
			"payload", task.Payload(),
			"status", fmt.Errorf("failed to list buckets: %w", err),
		)
		return err
	}

	// Specify the directory to save the backups.
	if _, err := os.Stat(payload.BackupDirectory); os.IsNotExist(err) {
		_ = processor.logger.Log(
			"type", task.Type(),
			"payload", task.Payload(),
			"status", fmt.Errorf("backup directory does not exist: %w", err),
		)
		return err
	}

	// Loop through each bucket and backup its objects.
	for _, bucketInfo := range buckets {
		bucketName := bucketInfo.Name
		objectsCh := minioClient.ListObjects(ctx, bucketName, minio.ListObjectsOptions{})

		for object := range objectsCh {
			if object.Err != nil {
				_ = processor.logger.Log(
					"type", task.Type(),
					"payload", task.Payload(),
					"status", fmt.Errorf("failed to list objects: %w", object.Err),
				)

				if payload.SkipErrors {
					continue
				} else {
					return object.Err
				}
			}

			// Create the directory structure to match the bucket name.
			backupBucketDir := fmt.Sprintf("%s/%s", payload.BackupDirectory, bucketName)
			if _, err := os.Stat(backupBucketDir); os.IsNotExist(err) {
				err := os.MkdirAll(backupBucketDir, fs.ModePerm)
				if err != nil {
					_ = processor.logger.Log(
						"type", task.Type(),
						"payload", task.Payload(),
						"status", fmt.Errorf("failed to create directory %s: %w", backupBucketDir, err),
					)
					return err
				}
			}

			// Download the object to the specified directory.
			objectName := object.Key
			objectPath := fmt.Sprintf("%s/%s", backupBucketDir, objectName)
			err = minioClient.FGetObject(ctx, bucketName, objectName, objectPath, minio.GetObjectOptions{})
			if err != nil {
				_ = processor.logger.Log(
					"type", task.Type(),
					"payload", task.Payload(),
					"status", fmt.Errorf("failed to download object %s: %w", objectName, err),
				)
				if payload.SkipErrors {
					continue
				} else {
					return err
				}
			}
			_ = processor.logger.Log(
				"type", task.Type(),
				"payload", task.Payload(),
				"status", fmt.Sprintf("successfully downloaded %s from bucket %s\n", objectName, bucketName),
			)
		}
	}

	_ = processor.logger.Log(
		"type", task.Type(),
		"payload", task.Payload(),
		"status", "minio backup task completed successfully",
	)

	return nil
}
