package stringutil

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"errors"
	"io"

	"golang.org/x/crypto/bcrypt"
)

// table is a variable that represents an array of bytes containing the digits '1' to '0'.
var table = [...]byte{'1', '2', '3', '4', '5', '6', '7', '8', '9', '0'}

// DefaultCharset is the default charset used for generating random strings.
const DefaultCharset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789@$#%&._-"

// RandDigits generates a sequence of digit string with the expected length.
//
// Parameters:
// - length: The length of the digit string to generate.
//
// Returns:
// - string: The generated digit string.
//
// Panic:
// - If the length of the generated string is not equal to the expected length, it will panic.
func RandDigits(length int) (string, error) {
	b := make([]byte, length)
	n, err := io.ReadAtLeast(rand.Reader, b, length)
	if n != length {
		return "", err
	}
	for i := 0; i < len(b); i++ {
		b[i] = table[int(b[i])%len(table)]
	}
	return string(b), nil
}

// Generate unique 16 char length credit card number and fixed prefix
const ccPrefix = "4111"

/*
GenCreditCard generates a random credit card number based on the Luhn algorithm.

The function uses the RandDigits function to generate a random 12-digit number and appends it to the ccPrefix variable. The resulting string is returned as the credit card number.

Returns:
- string: The randomly generated credit card number.

Example:

	cardNumber := GenCreditCard()
	fmt.Println(cardNumber) // Output: "123456789012"

Note:
- The ccPrefix variable should be set before calling this function to ensure the generated credit card number has the desired prefix.
- The RandDigits function is used to generate the random digits of the credit card number.
*/
func GenCreditCard() (string, error) {
	digits, err := RandDigits(12)
	if err != nil {
		return "", err
	}
	cardNumber := ccPrefix + digits

	return cardNumber, nil
}

// HashString returns the hashed string of the given string using the bcrypt algorithm.

// Parameters:
// - val: The string to be hashed.

// Returns:
// - string: The hashed string.
// - error: An error if the hashing process fails.

// Example:
//   hashedString, err := HashString("password")
//   if err != nil {
//     fmt.Println("Error:", err)
//   } else {
//     fmt.Println("Hashed String:", hashedString)
//   }

// Note:
// - The bcrypt algorithm is used to generate the hashed string.
// - The generated hashed string can be used for password storage and comparison.
// Cost factor for bcrypt - higher is more secure but slower
const BcryptCost = 12

func HashString(val string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(val), BcryptCost)
	return string(bytes), err
}

// CheckHashedString compares a hashed string with a specific plain string.
//
// Parameters:
// - plain: The plain string to compare with the hashed string.
// - hash: The hashed string to compare with the plain string.
//
// Returns:
// - bool: True if the plain string matches the hashed string, false otherwise.
//
// Example:
//
//	result := CheckHashedString("password", "$2a$10$SOMEHASHEDSTRING")
//	fmt.Println(result) // Output: true
//
// Note:
// - The function uses the bcrypt.CompareHashAndPassword function to compare the plain string with the hashed string.
// - It returns true if the plain string matches the hashed string, and false otherwise.
func CheckHashedString(plain, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(plain))
	return err == nil
}

// EncryptText encrypts the given string using a specific secret key.

// Parameters:
// - value: The string to be encrypted.
// - secret: The secret key used for encryption.

// Returns:
// - string: The encrypted string in base64 format.
// - error: An error if the encryption process fails.

// Example:
//   encryptedString, err := EncryptText("Hello, World!", "mySecretKey")
//   if err != nil {
//     fmt.Println("Error:", err)
//   } else {
//     fmt.Println("Encrypted String:", encryptedString)
//   }

// Note:
// - The function uses the AES encryption algorithm to encrypt the string.
// - The secret key should be a byte array of the correct length.
// - The encrypted string is returned in base64 format for easy storage and transmission.
func EncryptText(value, secret string) (string, error) {
	text := []byte(value)
	key := []byte(secret)

	c, err := aes.NewCipher(key)
	if err != nil {
		return "", errors.New("secret is not in correct length")
	}

	gcm, err := cipher.NewGCM(c)
	if err != nil {
		return "", err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}
	res := gcm.Seal(nonce, nonce, text, nil)
	b64 := base64.URLEncoding.EncodeToString(res)
	return b64, nil
}

// DecryptText decrypts the given cipher string using the specified secret key.

// Parameters:
// - value: The cipher string to be decrypted.
// - secret: The secret key used for decryption.

// Returns:
// - string: The decrypted plaintext string.
// - error: An error if the decryption process fails.

// Example:
//   plaintext, err := DecryptText("encryptedString", "mySecretKey")
//   if err != nil {
//     fmt.Println("Error:", err)
//   } else {
//     fmt.Println("Decrypted Text:", plaintext)
//   }

// Note:
// - The function uses the AES encryption algorithm to decrypt the cipher string.
// - The secret key should be a byte array of the correct length.
// - The cipher string should be in base64 format.
// - The decrypted plaintext string is returned as a result.
func DecryptText(value, secret string) (string, error) {
	ciphertext, err := base64.URLEncoding.DecodeString(value)
	if err != nil {
		return "", err
	}

	key := []byte(secret)

	c, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(c)
	if err != nil {
		return "", err
	}

	nonceSize := gcm.NonceSize()
	if len(ciphertext) < nonceSize {
		return "", nil
	}

	nonce, ciphertext := ciphertext[:nonceSize], ciphertext[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}

// RandomPassword generates a random string with the given length from the given charset.
//
// Parameters:
// - length: The length of the random string to generate.
// - charset: The charset from which the random string will be generated. If empty, the DefaultCharset will be used.
//
// Returns:
// - string: The randomly generated string.
// - error: An error if the generation process fails.
//
// Example:
//
//	password, err := RandomPassword(8, "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789@$#%&._-")
//	if err != nil {
//	  fmt.Println("Error:", err)
//	} else {
//	  fmt.Println("Random Password:", password)
//	}
//
// Note:
// - If the charset is empty, the DefaultCharset will be used, which contains a combination of lowercase and uppercase letters, digits, and special characters.
// - The function uses the crypto/rand package to generate random bytes, which are then converted to characters from the charset.
// - The generated random string is returned along with any error that occurred during the generation process.
func RandomPassword(length int, charset string) (string, error) {
	if len(charset) == 0 {
		charset = DefaultCharset
	}

	bytes := make([]byte, length)
	_, err := io.ReadFull(rand.Reader, bytes)
	if err != nil {
		return "", err
	}
	for i, b := range bytes {
		bytes[i] = charset[b%byte(len(charset))]
	}
	return string(bytes), nil
}
