package writer

import (
	"fmt"
	"io"
	"log"

	"github.com/liveutil/go-lib/env"
	"github.com/nats-io/nats.go"
)

type natsLogWriter struct {
	conn    *nats.Conn
	subject string
}

func NewNatsLogWriter(conn *nats.Conn, subject string) io.Writer {
	if !env.IsProduction() {
		_, err := conn.Subscribe(subject, func(m *nats.Msg) {
			fmt.Printf("%s\n", string(m.Data))
		})

		if err != nil {
			log.Fatal(err)
		}
	}

	return &natsLogWriter{
		conn:    conn,
		subject: subject,
	}
}

// Write implements io.Writer.
func (w natsLogWriter) Write(p []byte) (n int, err error) {
	err = w.conn.Publish(w.subject, p)
	if err != nil {
		return
	}

	return len(p), nil
}
