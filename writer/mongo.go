package writer

import (
	"context"
	"encoding/json"
	"io"

	"go.mongodb.org/mongo-driver/mongo"
)

type mongoLogWriter struct {
	client   *mongo.Client
	database string
	coll     *mongo.Collection
}

func NewMongoLogWriter(client *mongo.Client, database string) io.Writer {
	return &mongoLogWriter{
		client:   client,
		database: database,
		coll:     client.Database(database).Collection(database),
	}
}

// Write implements io.Writer.
func (m *mongoLogWriter) Write(p []byte) (n int, err error) {
	var record interface{}

	err = json.Unmarshal(p, &record)
	if err != nil {
		return
	}

	_, err = m.coll.InsertOne(context.Background(), record)
	if err != nil {
		return
	}

	return len(p), nil
}
