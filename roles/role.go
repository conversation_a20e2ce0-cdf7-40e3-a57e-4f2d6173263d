package roles

const (
	UserRole       string = "USER"
	IssuerRole     string = "ISSUER"
	AdminRole      string = "ADMIN"
	SuperAdminRole string = "SUPER_ADMIN"
	SupportRole    string = "SUPPORT"
	PosDeviceRole  string = "POS_DEVICE"
)

// Define role sets to easy management.
var (
	AllRoles        = []string{UserRole, IssuerRole, AdminRole, SuperAdminRole, SupportRole, PosDeviceRole}
	AdminRoles      = []string{AdminRole, SuperAdminRole}
	SuperAdminRoles = []string{SuperAdminRole}
	SupportRoles    = []string{SupportRole}
	IssuerRoles     = []string{IssuerRole}
	UserRoles       = []string{UserRole}
	PosDeviceRoles  = []string{PosDeviceRole}
)
