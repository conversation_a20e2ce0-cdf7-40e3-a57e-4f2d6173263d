package contextutil

import (
	"context"
	"errors"

	"github.com/mitchellh/mapstructure"
)

type ContextKey string

const (
	ContextClaimsKey    ContextKey = "CONTEXT_CLAIMS"
	ContextRoleKey      ContextKey = "CONTEXT_ROLE"
	ContextUserKey      ContextKey = "CONTEXT_USER"
	ContextUserIDKey    ContextKey = "CONTEXT_USER_ID"
	ContextRemoteIPKey  ContextKey = "CONTEXT_REMOTE_IP"
	ContextSessionKey   ContextKey = "CONTEXT_SESSION"
	ContextClientIDKey  ContextKey = "CONTEXT_CLIENT_ID"
	ContextUserAgentKey ContextKey = "CONTEXT_USER_AGENT"
	AdminRoleName       ContextKey = "ADMIN"
)

var (
	AdminRoles      []ContextKey = []ContextKey{"ADMIN", "SUPER_ADMIN"}
	SuperAdminRoles []ContextKey = []ContextKey{"SUPER_ADMIN"}
	SuperAdminRole  ContextKey   = "SUPER_ADMIN"
	AdminRole       ContextKey   = "ADMIN"
	UserRole        ContextKey   = "USER"
	IssuerRole      ContextKey   = "ISSUER"
	SupportRole     ContextKey   = "SUPPORT"
	PosDeviceRole   ContextKey   = "POS_DEVICE"
)

// CatchUser retrieves the user object from the gin context.
//
// Parameters:
// - ctx: The gin context object.
// - input: The input object to decode the user object into.
//
// Returns:
// - error: An error if the user object is not found in the context or if there is an error while decoding the object.
//
// Example:
//
//	err := CatchUser(ctx, &user)
//	if err != nil {
//	  // Handle error
//	}
//	// Use the user object
//
// Note:
// This function assumes that the user object is stored in the gin context using the key "CONTEXT_USER".
// It uses the mapstructure package to decode the user object into the provided input object.
// If there is an error while decoding, the function returns the error.
// If the user object is not found in the context, the function returns an error with the message "user not found in context".
func CatchUser(ctx context.Context, input interface{}) error {
	value := ctx.Value(ContextUserKey)
	if value == nil {
		return errors.New("user not found in context")
	}

	return mapstructure.Decode(value, input)
}

// GetString returns the value associated with the key as a string.
func GetString(ctx context.Context, key ContextKey) (s string) {
	s, ok := ctx.Value(key).(string)
	if !ok {
		return ""
	}

	return
}

// RemoteIP parses the IP from Request.RemoteAddr, normalizes and returns the IP (without the port).
func RemoteIP(ctx context.Context) string {
	return GetString(ctx, ContextRemoteIPKey)
}

// UserAgent returns the client's User-Agent, if sent in the request.
func UserAgent(ctx context.Context) string {
	return GetString(ctx, ContextUserAgentKey)
}

// GetHeaderFromContext retrieves the value of a specified header from context.Context
func GetHeaderFromContext(ctx context.Context, headerKey string) ([]string, bool) {
	headers, ok := ctx.Value("CONTEXT_HEADERS").(map[string][]string)
	if !ok {
		return nil, false
	}
	headerValue, exists := headers[headerKey]
	return headerValue, exists
}
