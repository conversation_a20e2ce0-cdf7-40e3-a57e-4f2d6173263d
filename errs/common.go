package errs

import "errors"

var (
	ErrInternalServer                 = errors.New("internal server error")
	ErrUserBannedOrNotFount           = errors.New("user with specified email/mobile not found or is banned")
	ErrTooManyLoginRequest            = errors.New("too many login request attempt")
	ErrSpecifiedAccessRoleNotGranted  = errors.New("specified access role is not granted")
	ErrRateLimitExceeded              = errors.New("rate limit exceeded")
	ErrCreateUser                     = errors.New("an error occurred on creating new user")
	ErrUserAlreadyExist               = errors.New("user with specified email/mobile already exist")
	ErrInvalidVerificationCode        = errors.New("specified verification code is not valid")
	ErrVerificationCodeExpired        = errors.New("specified verification code is expired")
	ErrAuthorizationFailed            = errors.New("authorization failed")
	ErrSessionNotFoundOrExpired       = errors.New("session not found or expired")
	ErrMapstructureError              = errors.New("mapping structures error")
	ErrDuplicatedUniqueProperties     = errors.New("specified unique properties already exist")
	ErrDependedAccountsContractDelete = errors.New("contract cannot be deleted, one or more account depended on it")
	ErrIdempotencyKeyIsNotProvided    = errors.New("idempotency key header is not provided")
)
