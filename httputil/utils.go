package httputil

import (
	"errors"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func GetPathSizeOffset(ctx *gin.Context) (uint32, uint32) {
	return GetPathSize(ctx), GetPathOffset(ctx)
}

func GetPathSize(ctx *gin.Context) uint32 {
	s, ok := ctx.Params.Get("size")
	if !ok {
		return 0
	}

	sn, err := strconv.ParseUint(s, 10, 32)
	if err != nil {
		return 0
	}

	if sn < 1 {
		return 100
	}

	return uint32(sn)
}

func GetPathOffset(ctx *gin.Context) uint32 {
	s, ok := ctx.Params.Get("offset")
	if !ok {
		return 0
	}

	sn, err := strconv.ParseUint(s, 10, 32)
	if err != nil {
		return 0
	}

	return uint32(sn)
}

func GetPathID(ctx *gin.Context) (int64, error) {
	value, ok := ctx.Params.Get("id")
	if !ok {
		return 0, errors.New("no path param with :id name found")
	}

	id, err := strconv.ParseInt(value, 10, 64)
	if err != nil {
		return 0, errors.New("failed to parse :id value to int64 type")
	}

	if id < 1 {
		return 0, errors.New("invalid :id value")
	}

	return id, nil
}

func GetPathInt64(name string, ctx *gin.Context) (int64, error) {
	value, ok := ctx.Params.Get(name)
	if !ok {
		return 0, errors.New("no path param with " + name + " name found")
	}

	res, err := strconv.ParseInt(value, 10, 64)
	if err != nil {
		return 0, errors.New("failed to parse " + name + " value to int64 type")
	}

	// Validate the value is positive
	if res < 0 {
		return 0, errors.New("value for " + name + " must be positive")
	}

	return res, nil
}

func GetPathBoolean(name string, ctx *gin.Context) (bool, error) {
	value, ok := ctx.Params.Get(name)
	if !ok {
		return false, errors.New("no path param with " + name + " name found")
	}

	if value != "true" && value != "false" {
		return false, errors.New("failed to parse " + name + " value to boolean type, value should be 'true' or 'false'")
	}

	return value == "true", nil
}

func GetPathUInt64(name string, ctx *gin.Context) (uint64, error) {
	value, ok := ctx.Params.Get(name)
	if !ok {
		return 0, errors.New("no path param with " + name + " name found")
	}

	// Validate input is numeric before parsing
	for _, c := range value {
		if c < '0' || c > '9' {
			return 0, errors.New("value for " + name + " must contain only digits")
		}
	}

	res, err := strconv.ParseUint(value, 10, 64)
	if err != nil {
		return 0, errors.New("failed to parse " + name + " value to uint64 type")
	}

	return res, nil
}

func GetPathString(name string, ctx *gin.Context) (string, error) {
	value, ok := ctx.Params.Get(name)
	if !ok {
		return "", errors.New("no path param with " + name + " name found")
	}

	// Validate string is not empty
	if len(strings.TrimSpace(value)) == 0 {
		return "", errors.New("value for " + name + " cannot be empty")
	}

	return value, nil
}

func GetIdempotencyKey(ctx *gin.Context) (uuid.UUID, error) {
	ik := ctx.GetHeader("X-Idempotency-Key")
	return uuid.Parse(ik)
}

func GetPathInt(name string, ctx *gin.Context) (int, error) {
	value, ok := ctx.Params.Get(name)
	if !ok {
		return 0, errors.New("no path param with " + name + " name found")
	}

	res, err := strconv.ParseInt(value, 10, 32)
	if err != nil {
		return 0, errors.New("failed to parse " + name + " value to int type")
	}

	// Validate the value is positive
	if res < 0 {
		return 0, errors.New("value for " + name + " must be positive")
	}

	return int(res), nil
}
