// Package httpserver implements HTTP Server.
package httputil

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

const (
	_defaultReadTimeout     = 15 * time.Second
	_defaultWriteTimeout    = 15 * time.Second
	_defaultIdleTimeout     = 60 * time.Second
	_defaultMaxHeaderBytes  = 1 << 20 // 1 MB
	_defaultAddr            = ":80"
	_defaultShutdownTimeout = 5 * time.Second
)

type Server struct {
	server          *http.Server
	notify          chan error
	shutdownTimeout time.Duration
	Router          *gin.Engine
}

func New(host string, port int, router *gin.Engine) *Server {
	server := prepareHttpServer(host, port, router)
	server.start()

	return server
}

func prepareHttpServer(host string, port int, router *gin.Engine) *Server {
	httpServer := &http.Server{
		Handler:        router,
		ReadTimeout:    _defaultReadTimeout,
		WriteTimeout:   _defaultWriteTimeout,
		IdleTimeout:    _defaultIdleTimeout,
		MaxHeaderBytes: _defaultMaxHeaderBytes,
		Addr:           _defaultAddr,
	}

	if host != "" && port != 0 {
		httpServer.Addr = net.JoinHostPort(host, fmt.Sprint(port))
	}

	return &Server{
		server:          httpServer,
		notify:          make(chan error, 1),
		shutdownTimeout: _defaultShutdownTimeout,
		Router:          router,
	}
}

func (s *Server) start() {
	go func() {
		s.notify <- s.server.ListenAndServe()
		close(s.notify)
	}()
}

// StartTLS starts the server with TLS configuration
func (s *Server) StartTLS(certFile, keyFile string) {
	go func() {
		s.notify <- s.server.ListenAndServeTLS(certFile, keyFile)
		close(s.notify)
	}()
}

func (s *Server) Notify() <-chan error {
	return s.notify
}

func (s *Server) Shutdown() error {
	ctx, cancel := context.WithTimeout(context.Background(), s.shutdownTimeout)
	defer cancel()

	return s.server.Shutdown(ctx)
}
