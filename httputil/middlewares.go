package httputil

import (
	"github.com/gin-gonic/gin"
)

// NormalizeContext middleware for cloning context values to new context
func NormalizeGinContext(headersKey, remoteIPKey, userAgentKey string) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		headers := headers(ctx)

		ctx.Set(headersKey, headers)
		ctx.Set(remoteIPKey, ctx.ClientIP())
		ctx.Set(userAgentKey, ctx.Request.UserAgent())

		ctx.Next()
	}
}

// headers clones headers from gin.Context to a map
func headers(c *gin.Context) map[string][]string {
	headers := make(map[string][]string)
	for k, v := range c.Request.Header {
		headers[k] = v
	}
	return headers
}
