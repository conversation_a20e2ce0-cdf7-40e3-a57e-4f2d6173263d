package pgutil

import (
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

type (
	NilTime struct {
		Time time.Time
	}
)

// GetPGInt8 converts int64 to pgtype.Int8 with Valid = true
func GetPGInt8(id int64) pgtype.Int8 {
	return pgtype.Int8{Int64: id, Valid: true}
}

// GetPGText converts string to pgtype.Text with Valid = true
func GetPGText(value string) pgtype.Text {
	return pgtype.Text{String: value, Valid: true}
}

// GetPGTimestamptz converts time to pgtype.Timestamptz with Valid = true
func GetPGTimestamptz(value time.Time) pgtype.Timestamptz {
	return pgtype.Timestamptz{Time: value, Valid: true}
}
