// Package postgres implements postgres connection.
package pgutil

import (
	"context"
	"fmt"
	"sync"
	"time"

	kitLog "github.com/go-kit/log"
	"github.com/jackc/pgx/v5/pgxpool"
)

const (
	_defaultConnAttempts = 10
	_defaultConnTimeout  = time.Second * 3
)

// Postgres -.
type Postgres struct {
	maxPoolSize  int
	connAttempts int
	connTimeout  time.Duration

	Pool *pgxpool.Pool
}

var pg *Postgres
var handleOnce sync.Once

func NewOrGetSingleton(maxPoolSize int, connAttempts int, connTimeout time.Duration, dbSource string, logger kitLog.Logger) *Postgres {
	handleOnce.Do(func() {
		postgres, err := initPostgres(maxPoolSize, connAttempts, connTimeout, dbSource, logger)
		if err != nil {
			panic(err)
		}

		pg = postgres
	})

	return pg
}

func initPostgres(maxPoolSize int, connAttempts int, connTimeout time.Duration, dbSource string, logger kitLog.Logger) (*Postgres, error) {
	pg = &Postgres{
		maxPoolSize:  maxPoolSize,
		connAttempts: connAttempts,
		connTimeout:  connTimeout,
	}

	poolConfig, err := pgxpool.ParseConfig(dbSource)
	if err != nil {
		return nil, fmt.Errorf("pgxpool.ParseConfig: %w", err)
	}

	poolConfig.MaxConns = int32(pg.maxPoolSize)

	// Add connection health check
	poolConfig.HealthCheckPeriod = 1 * time.Minute

	attempts := pg.connAttempts
	if attempts == 0 {
		attempts = _defaultConnAttempts
	}

	backoffFactor := 1.5
	currentTimeout := pg.connTimeout

	for attempts > 0 {
		pg.Pool, err = pgxpool.NewWithConfig(context.Background(), poolConfig)
		if err == nil {
			break
		}

		_ = logger.Log(
			"msg", "Postgres connection failed, retrying",
			"attempts_left", attempts,
			"backoff_seconds", currentTimeout.Seconds(),
			"error", err,
		)

		// Exponential backoff
		time.Sleep(currentTimeout)
		currentTimeout = time.Duration(float64(currentTimeout) * backoffFactor)
		attempts--
	}

	if err != nil {
		return nil, fmt.Errorf("postgres - NewPostgres - connAttempts == 0: %w", err)
	}

	return pg, nil
}

func (p *Postgres) Close() {
	if p.Pool != nil {
		p.Pool.Close()
	}
}
