package paseto

import (
	"time"
)

// Maker is an interface for managing tokens
type Maker interface {
	// CreateToken creates a new token for a specific username and duration
	CreateToken(username string, duration time.Duration) (string, TokenClaims, error)

	// VerifyToken checks if the token is valid or not
	VerifyToken(token string) (TokenClaims, error)

	// VerifyToken checks if the token is valid or not
	EncodeClaims(payload *TokenClaims) (string, error)
}
