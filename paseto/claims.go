package paseto

import (
	"errors"
	"time"

	"github.com/google/uuid"
)

// Different types of error returned by the VerifyToken function
var (
	ErrInvalidToken = errors.New("token is invalid")
	ErrExpiredToken = errors.New("token has expired")
)

// TokenClaims contains the payload data of the token
type TokenClaims struct {
	ID        int64     `json:"id"`
	Subject   string    `json:"sub"`
	JTI       string    `json:"jti"`
	Issuer    string    `json:"iss"`
	Audience  string    `json:"aud"`
	IssuedAt  time.Time `json:"issued_at"`
	ExpiresAt time.Time `json:"exp"`
	ClientID  string    `json:"client_id"`
	Roles     []string  `json:"role"`
}

// NewClaims creates a new token payload with the given issuer, audience, username, and duration.
// It generates a random token ID using the uuid.NewRandom() function.
// If an error occurs during the generation of the token ID, it returns nil and the error.
// The payload contains the generated token ID, username, current time as the issued time, and the expiration time calculated by adding the given duration to the current time.
// The audience and issuer fields are set to the provided values.
// It returns the created payload and nil error if successful.
// Otherwise, it returns nil and the error that occurred during the generation of the token ID.
func NewClaims(issuer, audience, username string, duration time.Duration) (claims TokenClaims, err error) {
	tokenID, err := uuid.NewRandom()
	if err != nil {
		return claims, err
	}

	claims = TokenClaims{
		JTI:       tokenID.String(),
		Subject:   username,
		IssuedAt:  time.Now(),
		ExpiresAt: time.Now().Add(duration),
		Audience:  audience,
		Issuer:    issuer,
	}
	return claims, nil
}

// Valid checks if the token payload is valid or not
func (tc *TokenClaims) Valid() error {
	if time.Now().After(tc.ExpiresAt) {
		return ErrExpiredToken
	}
	return nil
}
