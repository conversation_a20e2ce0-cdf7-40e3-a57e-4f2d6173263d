package paseto

import (
	"fmt"
	"time"

	"github.com/aead/chacha20poly1305"
	"github.com/o1egl/paseto"
)

// pasetoMaker is a PASETO token maker
type pasetoMaker struct {
	paseto       *paseto.V2
	symmetricKey []byte
	Issuer       string
	Audience     string
}

// NewPasetoMaker creates a new PASETO token maker with the given symmetric key, issuer, and audience.
// The symmetric key must have a length of exactly chacha20poly1305.KeySize characters.
// If the key size is invalid, an error is returned.
// The returned Maker interface can be used to create and verify PASETO tokens.
//
// Parameters:
// - symmetricKey: The symmetric key used for encryption and decryption of the tokens.
// - issuer: The issuer of the tokens.
// - audience: The intended audience of the tokens.
//
// Returns:
// - Maker: The PASETO token maker.
// - error: An error if the symmetric key size is invalid.
func NewPasetoMaker(symmetricKey string, issuer, audience string) (Maker, error) {
	if len(symmetricKey) != chacha20poly1305.KeySize {
		return nil, fmt.Errorf("invalid key size: must be exactly %d characters", chacha20poly1305.KeySize)
	}

	maker := &pasetoMaker{
		paseto:       paseto.NewV2(),
		symmetricKey: []byte(symmetricKey),
		Issuer:       issuer,
		Audience:     audience,
	}

	return maker, nil
}

// CreateToken creates a new token for a specific username and duration
func (maker *pasetoMaker) CreateToken(username string, duration time.Duration) (string, TokenClaims, error) {
	claims, err := NewClaims(maker.Issuer, maker.Audience, username, duration)
	if err != nil {
		return "", claims, err
	}

	token, err := maker.paseto.Encrypt(maker.symmetricKey, claims, nil)
	return token, claims, err
}

// EncodeClaims encodes the claims into a PASETO token
func (maker *pasetoMaker) EncodeClaims(claims *TokenClaims) (string, error) {
	return maker.paseto.Encrypt(maker.symmetricKey, claims, nil)
}

// VerifyToken checks if the token is valid or not
func (maker *pasetoMaker) VerifyToken(token string) (claims TokenClaims, err error) {
	err = maker.paseto.Decrypt(token, maker.symmetricKey, &claims, nil)
	if err != nil {
		return claims, ErrInvalidToken
	}

	err = claims.Valid()
	if err != nil {
		return claims, err
	}

	return claims, nil
}
