package structure

import (
	"testing"

	"github.com/magiconair/properties/assert"
)

type DstStruct struct {
	Username string `json:"username"`
	Password string `json:"password" secure:"true"`
	Email    string `json:"email"`
}

type DstAnotherStruct struct {
	ID    int    `json:"id"`
	Token string `json:"token" secure:"true"`
}

// TestTypeConverter tests the TypeConverter function.
func TestTypeConverter(t *testing.T) {
	tests := []struct {
		name     string
		input    SampleStruct
		expected DstStruct
	}{
		{
			name: "SampleStruct",
			input: SampleStruct{
				Username: "john_doe",
				Password: "secret_password",
				Email:    "<EMAIL>",
			},
			expected: DstStruct{
				Username: "john_doe",
				Password: "secret_password",
				Email:    "<EMAIL>",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			expected, err := TypeConverter[DstStruct](tt.input)
			assert.Equal(t, tt.expected, expected)
			assert.Equal(t, err, nil)
		})
	}
}
