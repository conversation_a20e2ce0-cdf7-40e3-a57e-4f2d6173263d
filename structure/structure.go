package structure

import (
	"encoding/json"
	"errors"
)

/*
TypeConverter is a function that converts the given data into a pointer to a value of type R. It uses JSON marshaling and unmarshaling to perform the conversion.

Parameters:
- data: The data to be converted.

Returns:
- *R: A pointer to the converted value of type R.
- error: An error if the conversion fails.

Example:
result, err := TypeConverter(data)

	if err != nil {
	    // handle error
	}

// use result pointer to the converted value
*/
func TypeConverter[R any](data any) (R, error) {
	var result R
	b, err := json.Marshal(data)
	if err != nil {
		return result, err
	}

	err = json.Unmarshal(b, &result)
	if err != nil {
		return result, err
	}

	return result, err
}

/*
ConvertSlice is a function that converts a slice of any type into a slice of type R.

Parameters:
- data: The slice of any type to be converted.

Returns:
- []R: The converted slice of type R.
- error: An error if the conversion fails.

Example:
result, err := ConvertSlice(data)

	if err != nil {
	    // handle error
	}

// use result slice of type R
*/
func ConvertSlice[R any](data []any) ([]R, error) {
	result := make([]R, len(data))
	for i, v := range data {
		if value, ok := v.(R); ok {
			result[i] = value
		} else {
			return nil, errors.New("failed to convert slice")
		}
	}
	return result, nil
}

/*
Ptr is a function that takes a value of any type and returns a pointer to that value.

Parameters:
- v: The value of any type.

Returns:
- *T: A pointer to the value.

Example:
value := 10
pointer := Ptr(value)
fmt.Println(*pointer) // Output: 10
*/
func Ptr[T any](v T) *T {
	return &v
}
