package structure

import (
	"errors"
	"reflect"
)

// ConvertStruct converts src struct to the target struct type R by matching fields based on JSON tags,
// including nested fields.
func ConvertStruct[R any](src any) (R, error) {
	var result R
	if err := convert(reflect.ValueOf(src), reflect.ValueOf(&result).Elem()); err != nil {
		return result, err
	}
	return result, nil
}

// Helper function to recursively copy fields from src to dst based on JSON tags.
func convert(srcVal, dstVal reflect.Value) error {
	srcType := srcVal.Type()
	dstType := dstVal.Type()

	// Ensure both src and dst are structs
	if srcType.Kind() != reflect.Struct || dstType.Kind() != reflect.Struct {
		return errors.New("both source and destination must be structs")
	}

	// Map JSON tags in dst struct to its fields
	dstFieldMap := make(map[string]reflect.Value)
	for i := 0; i < dstVal.NumField(); i++ {
		field := dstType.Field(i)
		jsonTag := field.Tag.Get("json")
		if jsonTag != "" && jsonTag != "-" {
			dstFieldMap[jsonTag] = dstVal.Field(i)
		}
	}

	// Copy fields from src to dst based on JSON tags
	for i := 0; i < srcVal.NumField(); i++ {
		srcField := srcType.Field(i)
		srcFieldVal := srcVal.Field(i)
		jsonTag := srcField.Tag.Get("json")

		// Skip untagged or ignored fields
		if jsonTag == "" || jsonTag == "-" {
			continue
		}

		// Check if there's a matching field in the destination
		if dstField, ok := dstFieldMap[jsonTag]; ok {
			if srcFieldVal.Kind() == reflect.Struct && dstField.Kind() == reflect.Struct {
				// Recursively convert nested structs
				if err := convert(srcFieldVal, dstField); err != nil {
					return err
				}
			} else if dstField.IsValid() && dstField.CanSet() &&
				srcFieldVal.Type().ConvertibleTo(dstField.Type()) {
				// Set the field value directly if types are compatible
				dstField.Set(srcFieldVal.Convert(dstField.Type()))
			}
		}
	}

	return nil
}
