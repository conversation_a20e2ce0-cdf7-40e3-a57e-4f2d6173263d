package structure

import "reflect"

func RemoveSecureTags(args ...interface{}) {
	for _, arg := range args {
		val := reflect.ValueOf(arg)
		if val.Kind() == reflect.Ptr {
			val = val.Elem()
		}
		if val.Kind() == reflect.Struct {
			remove(val)
		}
	}
}

func remove(val reflect.Value) {
	t := val.Type()
	for i := 0; i < val.NumField(); i++ {
		field := val.Field(i)
		fieldType := t.Field(i)
		if fieldType.Tag.Get("secure") == "true" {
			field.Set(reflect.Zero(fieldType.Type))
		}
	}
}
