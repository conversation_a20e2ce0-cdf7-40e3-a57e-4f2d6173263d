package structure

import (
	"testing"

	"github.com/magiconair/properties/assert"
)

func TestConvertStruct(t *testing.T) {
	tests := []struct {
		name     string
		input    SourceStruct
		expected TargetStruct
	}{
		{
			name: "SampleStruct",
			input: SourceStruct{
				Name:    "john_doe",
				Age:     42,
				Address: Address{City: "New York", Country: "USA"},
			},
			expected: TargetStruct{
				Name: "john_doe",
				Age:  42,
				Location: Address{
					City:    "New York",
					Country: "USA",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			expected, err := ConvertStruct[TargetStruct](tt.input)
			assert.Equal(t, err, nil)
			assert.Equal(t, tt.expected, expected)
			assert.Equal(t, tt.expected.Location.City, expected.Location.City)
			assert.Equal(t, tt.expected.Location.Country, expected.Location.Country)
			assert.Equal(t, tt.expected.Name, expected.Name)
			assert.Equal(t, tt.expected.Age, expected.Age)
		})
	}
}
