package structure

import (
	"reflect"
	"testing"
)

func TestRemoveSecureTags(t *testing.T) {
	tests := []struct {
		name     string
		input    interface{}
		expected interface{}
	}{
		{
			name: "SampleStruct",
			input: &SampleStruct{
				Username: "john_doe",
				Password: "secret_password",
				Email:    "<EMAIL>",
			},
			expected: &SampleStruct{
				Username: "john_doe",
				Password: "",
				Email:    "<EMAIL>",
			},
		},
		{
			name: "AnotherStruct",
			input: &AnotherStruct{
				ID:    123,
				Token: "secure_token",
			},
			expected: &AnotherStruct{
				ID:    123,
				Token: "",
			},
		},
		{
			name: "MixedStructs",
			input: []interface{}{
				&SampleStruct{
					Username: "jane_doe",
					Password: "super_secret_password",
					Email:    "<EMAIL>",
				},
				&AnotherStruct{
					ID:    456,
					Token: "another_secure_token",
				},
			},
			expected: []interface{}{
				&SampleStruct{
					Username: "jane_doe",
					Password: "",
					Email:    "<EMAIL>",
				},
				&AnotherStruct{
					ID:    456,
					Token: "",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if slice, ok := tt.input.([]interface{}); ok {
				RemoveSecureTags(slice...)
				for i, input := range slice {
					expected := tt.expected.([]interface{})[i]
					if !reflect.DeepEqual(input, expected) {
						t.Errorf("removeSecureTags() = %v, want %v", input, expected)
					}
				}
			} else {
				RemoveSecureTags(tt.input)
				if !reflect.DeepEqual(tt.input, tt.expected) {
					t.Errorf("removeSecureTags() = %v, want %v", tt.input, tt.expected)
				}
			}
		})
	}
}
