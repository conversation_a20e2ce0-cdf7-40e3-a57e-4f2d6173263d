package structure

type SampleStruct struct {
	Username string `json:"username"`
	Password string `json:"password" secure:"true"`
	Email    string `json:"email"`
}

type AnotherStruct struct {
	ID    int    `json:"id"`
	Token string `json:"token" secure:"true"`
}

// Example structs with nested fields
type Address struct {
	City    string `json:"city"`
	Country string `json:"country"`
}

type SourceStruct struct {
	Name    string  `json:"name"`
	Age     int     `json:"age"`
	Address Address `json:"address"`
}

type TargetStruct struct {
	Name     string  `json:"name"`
	Age      int     `json:"age"`
	Location Address `json:"address"`
}
