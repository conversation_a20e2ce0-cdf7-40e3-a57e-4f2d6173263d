package configuration

import (
	"context"
	"encoding/json"
	"time"

	dapr "github.com/dapr/go-sdk/client"
)

func LoadDaprConfig[R comparable](daprHost, store, key string) (R, error) {
	client, err := dapr.NewClientWithAddress(daprHost)
	if err != nil {
		return *new(R), err
	}
	defer client.Close()

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	config, err := client.GetConfigurationItem(ctx, store, key)
	if err != nil {
		return *new(R), err
	}

	var result R
	err = json.Unmarshal([]byte(config.Value), &result)
	if err != nil {
		return *new(R), err
	}

	return result, nil
}
