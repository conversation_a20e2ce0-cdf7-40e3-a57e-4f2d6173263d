package configuration

import "github.com/spf13/viper"

// LoadConfig loads the configuration from the specified path.
// It adds the config path, sets the config name and type, and enables automatic environment variable reading.
// It reads the config file using viper and unmarshal it into a Config struct.
// If any error occurs during the process, it returns nil and the error.
// Otherwise, it returns the loaded Config struct and nil error.
func LoadEnvConfig[R any](path, configName, configType string) (R, error) {
	viper.AddConfigPath(path)
	viper.SetConfigName(configName)
	viper.SetConfigType(configType)

	viper.AutomaticEnv()

	var result R

	err := viper.ReadInConfig()
	if err != nil {
		return result, err
	}

	err = viper.Unmarshal(&result)
	if err != nil {
		return result, err
	}

	return result, nil
}
