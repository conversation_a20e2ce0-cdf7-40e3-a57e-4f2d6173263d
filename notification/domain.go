package notification

import (
	"context"
)

type NotificationType string

const (
	EmailNotification NotificationType = "email"
	SMSNotification   NotificationType = "sms"
	PushNotification  NotificationType = "push"
)

type NotificationMessage[T NotificationMessagePayload] struct {
	Type    NotificationType `json:"type"`
	Payload T                `json:"payload"`
}

type MailNotificationMessage struct {
	Subject string `json:"subject"`
	Content string `json:"content"`
	To      string `json:"to"`
}

type SMSNotificationMessage struct {
	Content       string   `json:"content"`
	To            string   `json:"to"`
	From          string   `json:"from"`
	PatternID     string   `json:"pattern_id"`
	PatternParams []string `json:"pattern_params"`
}

// ConsumerRepository defines the interface for consumer operations
type ConsumerRepository interface {
	// Observe returns a channel that receives messages from the specified address
	Observe(ctx context.Context, address string) (<-chan []byte, error)
	// Reply sends a message to the specified address
	Reply(ctx context.Context, address string, message []byte) error
}

// ConsumerService defines the interface for consumer operations
type ConsumerService interface {
	// Enqueue adds a message to the processing queue and returns an identifier for the message
	Enqueue(ctx context.Context, message []byte) (identifier string, err error)
	// Dequeue removes a message from the processing queue
	Dequeue(ctx context.Context, identifier string) error
	// Requeue moves a message from one position in the queue to another position in the other queue
	Requeue(ctx context.Context, previousIdentifier string) (newIdentifier string, err error)
}

type NotificationMessagePayload interface {
	Marshal() ([]byte, error)
	Unmarshal(T any) error
	Type() NotificationType
	Source() string
	Destination() string
	Content() string
}
