package notification

import "encoding/json"

func (sms *SMSNotificationMessage) Marshal() ([]byte, error) {
	json, err := json.Marshal(sms)
	if err != nil {
		return nil, err
	}
	return json, nil
}

func (sms *SMSNotificationMessage) Unmarshal(b []byte) error {
	return nil
}

func (sms *SMSNotificationMessage) Type() NotificationType {
	return SMSNotification
}

func (sms *SMSNotificationMessage) Source() string {
	return sms.From
}

func (sms *SMSNotificationMessage) Destination() string {
	return sms.To
}
