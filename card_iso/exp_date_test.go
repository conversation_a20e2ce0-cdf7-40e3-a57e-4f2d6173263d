package card_iso

import (
    "testing"
    "time"
)

func TestGetCardExpireMonthDayFromDate(t *testing.T) {
    tests := []struct {
        name          string
        input         time.Time
        expectedYear  string
        expectedMonth string
    }{
        {
            name:          "January 2025",
            input:         time.Date(2025, 1, 15, 0, 0, 0, 0, time.UTC),
            expectedYear:  "03", // 1403 in Shamsi calendar
            expectedMonth: "10", // Dey month
        },
        {
            name:          "December 2024",
            input:         time.Date(2024, 12, 20, 0, 0, 0, 0, time.UTC),
            expectedYear:  "03", // 1403 in Shamsi calendar
            expectedMonth: "09", // Azar month
        },
        {
            name:          "March 2026",
            input:         time.Date(2026, 3, 21, 0, 0, 0, 0, time.UTC),
            expectedYear:  "05", // 1405 in Shamsi calendar
            expectedMonth: "01", // Farvardin month
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            year, month := GetCardExpireMonthDayFromDate(tt.input)
            
            if year != tt.expectedYear {
                t.Errorf("GetCardExpireMonthDayFromDate() year = %v, want %v", year, tt.expectedYear)
            }
            if month != tt.expectedMonth {
                t.Errorf("GetCardExpireMonthDayFromDate() month = %v, want %v", month, tt.expectedMonth)
            }
        })
    }
}