// Package card_iso provides functionality for generating ISO 8583 compliant payment cards
// with proper CVV generation, track data, and EMV XML formatting.
//
// This package implements:
// - ISO 9797 Retail MAC algorithm for CVV generation
// - <PERSON><PERSON> algorithm for PAN validation
// - Track 1 and Track 2 magnetic stripe data generation
// - EMV XML profile generation
// - PKCS#11 HSM integration for secure key management
package card_iso

import (
	"crypto/cipher"
	"crypto/des"
	"encoding/hex"
	"encoding/xml"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"github.com/liveutil/go-lib/env"
	"github.com/miekg/pkcs11"
)

// IsoCardModel represents a complete payment card with all necessary data
// for both physical and digital card implementations.
type IsoCardModel struct {
	PAN        string // Primary Account Number (card number)
	ExpiryMMYY string // Expiry date in MMYY format
	Name       string // Cardholder name as it appears on the card

	// CVV values for different purposes:
	CVV1 string // CVV1 for magnetic stripe (service code 101)
	iCVV string // iCVV for chip cards (service code 201)
	CVV2 string // CVV2 for card-not-present transactions (service code 999)

	// Magnetic stripe track data:
	Track1 string // Track 1 data (contains name, PAN, expiry)
	Track2 string // Track 2 data (contains PAN, expiry, service data)

	// EMV and APDU data:
	APDUs  []string // Application Protocol Data Units for card programming
	EMVXML string   // EMV profile in XML format for chip personalization
}

// ISO9797RetailMAC generates a 4-digit CVV using the ISO 9797 Retail MAC algorithm.
// This is the standard algorithm used by payment card industry for generating
// Card Verification Values (CVV1, iCVV, CVV2).
//
// Parameters:
//   - pan: Primary Account Number (card number)
//   - expiry: Expiry date in MMYY format
//   - sc: Service Code (101 for CVV1, 201 for iCVV, 999 for CVV2)
//   - key: 3DES key (16 or 24 bytes)
//
// Returns:
//   - 4-digit CVV string
//   - error if key is invalid or encryption fails
//
// The algorithm:
// 1. Concatenates PAN + Expiry + Service Code
// 2. Pads the data to 8-byte boundary with zeros
// 3. Encrypts using 3DES-CBC with zero IV
// 4. Takes the last 8 bytes of ciphertext
// 5. Extracts first 4 numeric digits from hex representation
// 6. Pads with zeros if less than 4 digits found
func ISO9797RetailMAC(pan, expiry, sc string, key []byte) (string, error) {
	// Prepare input data: PAN + Expiry + Service Code, padded to 8-byte boundary
	raw := padTo8(pan + expiry + sc)

	// Normalize key to 24 bytes for 3DES
	k24, err := normalizeKey(key)
	if err != nil {
		return "", err
	}

	// Create 3DES cipher
	block, err := des.NewTripleDESCipher(k24)
	if err != nil {
		return "", err
	}

	// Use zero IV for CBC mode (as per ISO 9797 spec)
	iv := make([]byte, des.BlockSize)
	mode := cipher.NewCBCEncrypter(block, iv)

	// Encrypt the padded data
	ct := make([]byte, len(raw))
	mode.CryptBlocks(ct, []byte(raw))

	// Take last 8 bytes and convert to hex
	mac := hex.EncodeToString(ct[len(ct)-8:])

	// Extract first 4 numeric digits from hex string
	digits := ""
	for _, c := range mac {
		if c >= '0' && c <= '9' {
			digits += string(c)
			if len(digits) == 4 {
				break
			}
		}
	}

	// Pad with zeros if we found less than 4 digits
	for len(digits) < 4 {
		digits += "0"
	}

	return digits, nil
}

// padTo8 pads a string to the next 8-byte boundary by appending zeros.
// This is required for DES/3DES encryption which operates on 8-byte blocks.
//
// Example: "12345" becomes "12345000" (padded to 8 bytes)
func padTo8(s string) string {
	if rem := len(s) % 8; rem != 0 {
		s += strings.Repeat("0", 8-rem)
	}
	return s
}

// normalizeKey converts a 16-byte or 24-byte key to a 24-byte 3DES key.
// For 16-byte keys (2DES), it duplicates the first 8 bytes to create a 24-byte key.
// For 24-byte keys (3DES), it returns the key unchanged.
//
// This follows the standard 3DES key derivation:
// - 16-byte key: K1|K2 becomes K1|K2|K1 (2DES mode)
// - 24-byte key: K1|K2|K3 (full 3DES mode)
func normalizeKey(k []byte) ([]byte, error) {
	if len(k) == 16 {
		// 2DES: duplicate first 8 bytes to make 24-byte key
		return append(k, k[:8]...), nil
	}
	if len(k) == 24 {
		// Already 3DES format
		return k, nil
	}
	return nil, errors.New("key must be 16 or 24 bytes")
}

// genTrack generates magnetic stripe track data according to ISO/IEC 7813 standard.
//
// Track 1 format: %B<PAN>^<Name>^<Expiry><Service Code><Discretionary Data>?
// Track 2 format: ;<PAN>=<Expiry><Service Code><Discretionary Data>?
//
// Parameters:
//   - pan: Primary Account Number
//   - exp: Expiry date in MMYY format
//   - name: Cardholder name (for Track 1 only)
//
// Returns:
//   - track1: Track 1 data with name and full formatting
//   - track2: Track 2 data with numeric data only
func genTrack(pan, exp, name string) (string, string) {
	// Discretionary data for Track 1 (can be customized per issuer)
	d1 := "*********"
	// Track 1: %B + PAN + ^ + Name + ^ + Expiry + Service Code + Discretionary + ?
	t1 := fmt.Sprintf("%%B%s^%s^%s101%s?", pan, name, exp, d1)

	// Discretionary data for Track 2 (different from Track 1)
	d2 := "*********"
	// Track 2: ; + PAN + = + Expiry + Service Code + Discretionary + 0 + ?
	t2 := fmt.Sprintf(";%s=%s101%s0?", pan, exp, d2)

	return t1, t2
}

// genTrackAPDU generates APDU commands for writing track data to a smart card.
// These are example APDUs - actual commands depend on the card's file structure.
//
// Parameters:
//   - pad: Padded track data to be written
//
// Returns:
//   - Array of APDU command strings in hexadecimal format
func genTrackAPDU(pad string) []string {
	return []string{
		fmt.Sprintf("00DA0018%s", pad), // Example: Write Track1 data
		fmt.Sprintf("00DB0020%s", pad), // Example: Write Track2 data
	}
}

// toEMVXML converts card data to EMV XML format for chip personalization.
// This creates an XML profile containing essential EMV tags that can be used
// to personalize chip cards during manufacturing or issuance.
//
// EMV Tags included:
//   - Tag5A: Application Primary Account Number (PAN)
//   - Tag5F24: Application Expiration Date
//   - Tag57: Track 2 Equivalent Data
//   - Tag5F20: Cardholder Name
//
// Parameters:
//   - c: IsoCardModel containing the card data
//
// Returns:
//   - XML string with EMV profile
//   - error if XML marshaling fails
func toEMVXML(c *IsoCardModel) (string, error) {
	// Define XML structure for EMV tags
	type Tag struct {
		XMLName xml.Name
		Value   string `xml:",chardata"`
	}
	type Profile struct {
		XMLName xml.Name `xml:"EMVProfile"`
		Tags    []Tag    `xml:",any"`
	}

	// Create profile with essential EMV tags
	p := Profile{Tags: []Tag{
		{XMLName: xml.Name{Local: "Tag5A"}, Value: c.PAN},          // PAN
		{XMLName: xml.Name{Local: "Tag5F24"}, Value: c.ExpiryMMYY}, // Expiry
		{XMLName: xml.Name{Local: "Tag57"}, Value: c.Track2},       // Track 2
		{XMLName: xml.Name{Local: "Tag5F20"}, Value: c.Name},       // Cardholder Name
	}}

	// Marshal to indented XML
	out, err := xml.MarshalIndent(p, "", "  ")
	if err != nil {
		return "", err
	}

	// Return with XML header
	return xml.Header + string(out), nil
}

// GenerateCard creates a batch of payment cards with sequential account numbers.
// This function generates complete card data including PAN, CVV values, track data,
// and EMV XML profiles suitable for card production and personalization.
//
// Parameters:
//   - iin: Issuer Identification Number (first 4-6 digits of PAN)
//   - accSeq: Starting account sequence number
//   - count: Number of cards to generate
//   - exp: Expiry date in MMYY format
//   - name: Cardholder name
//   - key: 3DES key for CVV generation (16 or 24 bytes)
//   - p11Module: PKCS#11 module path (currently unused, reserved for HSM integration)
//
// Returns:
//   - Array of IsoCardModel structs containing complete card data
//   - error if any generation step fails
//
// The function generates:
//   - Sequential PANs with valid Luhn check digits
//   - CVV1 (magnetic stripe), iCVV (chip), CVV2 (card-not-present)
//   - Track 1 and Track 2 magnetic stripe data
//   - APDU commands for card programming
//   - EMV XML profile for chip personalization
func GenerateCard(iin string, accSeq, count uint64, exp, name string, key []byte, p11Module string) ([]*IsoCardModel, error) {
	cards := []*IsoCardModel{}

	for i := uint64(0); i < count; i++ {
		// Generate sequential account number (11 digits, zero-padded)
		acc := fmt.Sprintf("%011d", accSeq+i)

		// Create PAN: IIN + Account Number + Luhn check digit
		pan := iin + acc + computeLuhn(iin+acc)

		// Initialize card with basic data
		c := &IsoCardModel{PAN: pan, ExpiryMMYY: exp, Name: name}

		var err error

		// Generate CVV values using different service codes
		if c.CVV1, err = ISO9797RetailMAC(pan, exp, "101", key); err != nil {
			return nil, err
		}
		if c.iCVV, err = ISO9797RetailMAC(pan, exp, "201", key); err != nil {
			return nil, err
		}
		if c.CVV2, err = ISO9797RetailMAC(pan, exp, "999", key); err != nil {
			return nil, err
		}

		// Generate magnetic stripe track data
		c.Track1, c.Track2 = genTrack(pan, exp, name)

		// Generate APDU commands for card programming
		c.APDUs = genTrackAPDU(c.Track1 + c.Track2)

		// Generate EMV XML profile
		if c.EMVXML, err = toEMVXML(c); err != nil {
			return nil, err
		}

		cards = append(cards, c)
	}

	return cards, nil
}

// computeLuhn calculates the Luhn check digit for a given number string.
// The Luhn algorithm is used to validate credit card numbers and generate
// the final check digit that makes the entire PAN valid.
//
// Algorithm:
// 1. Starting from the rightmost digit, double every second digit
// 2. If doubling results in a number > 9, subtract 9
// 3. Sum all digits
// 4. The check digit is (10 - (sum mod 10)) mod 10
//
// Example: For "*********012345"
// - Doubled: 1,4,3,8,5,12,7,16,9,2,1,8,3,10,5
// - Adjusted: 1,4,3,8,5,3,7,7,9,2,1,8,3,1,5
// - Sum: 67, Check digit: (10 - 7) mod 10 = 3
//
// Parameters:
//   - number: String of digits (without check digit)
//
// Returns:
//   - Single digit string representing the Luhn check digit
func computeLuhn(number string) string {
	sum := 0
	double := false

	// Process digits from right to left
	for i := len(number) - 1; i >= 0; i-- {
		d := int(number[i] - '0')

		// Double every second digit
		if double {
			d *= 2
			// If result > 9, subtract 9 (equivalent to adding digits)
			if d > 9 {
				d -= 9
			}
		}

		sum += d
		double = !double
	}

	// Calculate check digit
	return strconv.Itoa((10 - (sum % 10)) % 10)
}

// Example of PKCS#11 SoftHSM loader
func readPKCS11Key(modulePath, pin, keyLabel string) ([]byte, error) {
	p := pkcs11.New(modulePath)
	if p == nil {
		return nil, errors.New("failed to init pkcs11")
	}
	if err := p.Initialize(); err != nil {
		return nil, err
	}
	defer p.Destroy()
	defer p.Finalize()

	session, err := p.OpenSession(pkcs11.CKF_SERIAL_SESSION|pkcs11.CKF_RW_SESSION, 0)
	if err != nil {
		return nil, err
	}
	defer p.CloseSession(session)

	if err := p.Login(session, pkcs11.CKU_USER, pin); err != nil {
		return nil, err
	}
	defer p.Logout(session)

	template := []*pkcs11.Attribute{
		pkcs11.NewAttribute(pkcs11.CKA_LABEL, keyLabel),
	}
	if err := p.FindObjectsInit(session, template); err != nil {
		return nil, err
	}
	objs, _, err := p.FindObjects(session, 1)
	if err != nil {
		return nil, err
	}
	p.FindObjectsFinal(session)
	if len(objs) == 0 {
		return nil, errors.New("key not found")
	}

	attrs, err := p.GetAttributeValue(session, objs[0], []*pkcs11.Attribute{
		pkcs11.NewAttribute(pkcs11.CKA_VALUE, nil),
	})
	if err != nil {
		return nil, err
	}
	return attrs[0].Value, nil
}

// ReadPKCS11KeyFromEnv reads PKCS#11 key parameters from environment variables
// and loads the key. This allows for different configurations in development
// and production environments.
//
// Environment variables used:
// - PKCS11_MODULE_PATH: Path to the PKCS#11 module library
// - PKCS11_PIN: PIN for the HSM
// - PKCS11_KEY_LABEL: Label of the key to retrieve
//
// If any required environment variable is missing, it falls back to the provided defaults.
func ReadPKCS11KeyFromEnv(defaultModulePath, defaultPin, defaultKeyLabel string) ([]byte, error) {
	modulePath := env.GetStringDefault("PKCS11_MODULE_PATH", defaultModulePath)
	pin := env.GetStringDefault("PKCS11_PIN", defaultPin)
	keyLabel := env.GetStringDefault("PKCS11_KEY_LABEL", defaultKeyLabel)

	return readPKCS11Key(modulePath, pin, keyLabel)
}

// GetCVVKeyFromEnv retrieves the CVV key either from an HSM (in production) or
// directly from environment variables (in development).
//
// In production mode, it uses PKCS#11 to access the HSM with parameters from environment variables.
// In development mode, it reads the key directly from the CARD_CVV_KEY environment variable.
//
// Environment variables:
// - ENVIRONMENT: Set to "production" to use HSM, any other value for development mode
// - CARD_CVV_KEY: Hex-encoded key for development (only used in non-production)
// - PKCS11_MODULE_PATH: Path to the PKCS#11 module (production only)
// - PKCS11_PIN: PIN for the HSM (production only)
// - PKCS11_KEY_LABEL: Label of the key to retrieve (production only)
func GetCVVKeyFromEnv() ([]byte, error) {
	// Check if we're in production mode
	if env.GetStringDefault("ENVIRONMENT", "development") == "production" {
		// Production mode - use HSM
		modulePath := env.GetStringDefault("PKCS11_MODULE_PATH", "/usr/local/lib/softhsm/libsofthsm2.so")
		pin := env.GetStringDefault("PKCS11_PIN", "1234")
		keyLabel := env.GetStringDefault("PKCS11_KEY_LABEL", "CVVKey")

		return readPKCS11Key(modulePath, pin, keyLabel)
	} else {
		// Development mode - read key directly from environment
		hexKey := env.GetStringDefault("CARD_CVV_KEY", "0*********ABCDEF0*********ABCDEF")
		return hex.DecodeString(hexKey)
	}
}

func RunExample() {
	// Example usage: replace module path and pin as needed
	key, err := GetCVVKeyFromEnv()
	if err != nil {
		panic(err)
	}
	cards, err := GenerateCard("5399", 1000, 3, "0628", "DOE/JOHN", key, "")
	if err != nil {
		panic(err)
	}
	for _, c := range cards {
		fmt.Printf("PAN: %s\nExpiry: %s\nCVV1: %s iCVV: %s CVV2: %s\nTrack1: %s\nTrack2: %s\nAPDUs: %v\nEMVXML:\n%s\n---\n",
			c.PAN, c.ExpiryMMYY, c.CVV1, c.iCVV, c.CVV2, c.Track1, c.Track2, c.APDUs, c.EMVXML)
	}
}
