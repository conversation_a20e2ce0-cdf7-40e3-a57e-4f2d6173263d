# Card ISO Package

This package provides functionality for generating ISO 8583 compliant payment cards with proper CVV generation, track data, and EMV XML formatting.

## Features

- **ISO 9797 Retail MAC Algorithm**: Standard CVV generation for payment cards
- **Luhn Algorithm**: PAN validation and check digit generation
- **Magnetic Stripe Data**: Track 1 and Track 2 generation according to ISO/IEC 7813
- **EMV XML Profiles**: Chip personalization data generation
- **PKCS#11 HSM Integration**: Secure key management support (planned)

## Core Components

### IsoCardModel

The main data structure representing a complete payment card:

```go
type IsoCardModel struct {
    PAN        string // Primary Account Number (16 digits)
    ExpiryMMYY string // Expiry date in MMYY format
    Name       string // Cardholder name
    CVV1       string // CVV1 for magnetic stripe (service code 101)
    iCVV       string // iCVV for chip cards (service code 201)
    CVV2       string // CVV2 for card-not-present transactions (service code 999)
    Track1     string // Track 1 magnetic stripe data
    Track2     string // Track 2 magnetic stripe data
    APDUs      []string // Application Protocol Data Units
    EMVXML     string   // EMV profile in XML format
}
```

### Key Functions

#### GenerateCard
Generates a batch of payment cards with sequential account numbers:

```go
func GenerateCard(iin string, accSeq, count uint64, exp, name string, key []byte, p11Module string) ([]*IsoCardModel, error)
```

**Parameters:**
- `iin`: Issuer Identification Number (first 4-6 digits)
- `accSeq`: Starting account sequence number
- `count`: Number of cards to generate
- `exp`: Expiry date in MMYY format
- `name`: Cardholder name
- `key`: 3DES key for CVV generation (16 or 24 bytes)
- `p11Module`: PKCS#11 module path (reserved for future HSM integration)

#### ISO9797RetailMAC
Generates CVV using the ISO 9797 Retail MAC algorithm:

```go
func ISO9797RetailMAC(pan, expiry, sc string, key []byte) (string, error)
```

**Service Codes:**
- `101`: CVV1 for magnetic stripe
- `201`: iCVV for chip cards
- `999`: CVV2 for card-not-present transactions

## Usage Examples

### Basic Card Generation

```go
package main

import (
    "fmt"
    "github.com/liveutil/go-lib/card_iso"
)

func main() {
    // 3DES key (16 bytes will be expanded to 24 bytes)
    key := []byte{0x01, 0x23, 0x45, 0x67, 0x89, 0xAB, 0xCD, 0xEF,
                  0x01, 0x23, 0x45, 0x67, 0x89, 0xAB, 0xCD, 0xEF}
    
    // Generate 10 cards starting from account sequence 1000
    cards, err := card_iso.GenerateCard(
        "4532",           // Visa IIN
        1000,             // Starting account sequence
        10,               // Number of cards
        "1225",           // Expiry MMYY
        "DOE/JOHN",       // Cardholder name
        key,              // 3DES key
        "",               // PKCS#11 module (unused)
    )
    
    if err != nil {
        panic(err)
    }
    
    for i, card := range cards {
        fmt.Printf("Card %d:\n", i+1)
        fmt.Printf("  PAN: %s\n", card.PAN)
        fmt.Printf("  CVV1: %s\n", card.CVV1)
        fmt.Printf("  iCVV: %s\n", card.iCVV)
        fmt.Printf("  CVV2: %s\n", card.CVV2)
        fmt.Printf("  Track1: %s\n", card.Track1)
        fmt.Printf("  Track2: %s\n", card.Track2)
        fmt.Printf("  EMV XML: %s\n", card.EMVXML)
        fmt.Println()
    }
}
```

### Individual CVV Generation

```go
// Generate CVV1 for magnetic stripe
cvv1, err := card_iso.ISO9797RetailMAC("****************", "1225", "101", key)

// Generate iCVV for chip
icvv, err := card_iso.ISO9797RetailMAC("****************", "1225", "201", key)

// Generate CVV2 for online transactions
cvv2, err := card_iso.ISO9797RetailMAC("****************", "1225", "999", key)
```

## Track Data Format

### Track 1 (IATA Format)
```
%B<PAN>^<Name>^<Expiry><Service Code><Discretionary Data>?
```
Example: `%B****************^DOE/JOHN^2512101123456789?`

### Track 2 (ABA Format)
```
;<PAN>=<Expiry><Service Code><Discretionary Data>0?
```
Example: `;****************=*****************?`

## EMV XML Profile

The generated EMV XML contains essential tags for chip personalization:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<EMVProfile>
  <Tag5A>****************</Tag5A>     <!-- PAN -->
  <Tag5F24>1225</Tag5F24>             <!-- Expiry Date -->
  <Tag57>;****************=*****************?</Tag57> <!-- Track 2 -->
  <Tag5F20>DOE/JOHN</Tag5F20>         <!-- Cardholder Name -->
</EMVProfile>
```

## Security Considerations

1. **Key Management**: Use proper 3DES keys (16 or 24 bytes)
2. **CVV Protection**: Different CVV values for different use cases
3. **PAN Generation**: Sequential account numbers with valid Luhn check digits
4. **HSM Integration**: Future support for PKCS#11 hardware security modules

## Testing

Run the comprehensive test suite:

```bash
go test -v
```

Run benchmarks:

```bash
go test -bench=.
```

## Performance

Benchmark results on Intel Xeon E5645 @ 2.40GHz:
- CVV Generation: ~10,223 ns/op
- Full Card Generation: ~42,512 ns/op  
- Luhn Calculation: ~40.14 ns/op

## Standards Compliance

- **ISO 9797**: Retail MAC algorithm for CVV generation
- **ISO/IEC 7813**: Magnetic stripe card format
- **ISO 8583**: Payment card industry standards
- **Luhn Algorithm**: Check digit validation
- **EMV**: Chip card specifications

## License

This package is part of the go-libs project and follows the same licensing terms.
