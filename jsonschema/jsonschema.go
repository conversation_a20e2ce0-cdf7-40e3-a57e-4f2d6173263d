package jsonschema

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"reflect"
	"runtime"
	"strings"

	"github.com/xeipuuv/gojsonschema"
)

var (
	ErrUnimplementedRequest = errors.New("ErrUnimplementedRequest")
)

const formatError string = "%s: is not in correct format or not provided."

// LoadJsonValidationSchemas loads JSON validation schemas from the specified path and populates the input map with the schema data.

// Parameters:
// - path: The path to the directory containing the JSON schema files.
// - input: A map to store the loaded schema data, where the key is the schema name and the value is the schema content.

// Returns:
// - error: An error indicating if there was an issue loading the schemas.

// Example Usage:
// err := LoadJsonValidationSchemas("/path/to/schemas", input)
// if err != nil {
//     // handle error
// }

// Note:
// - The path should be a valid directory path.
// - The input map will be modified in-place with the loaded schema data.
// - The schema files should have the ".json" extension.
func LoadJsonValidationSchemas(path string, input map[string][]byte) (err error) {
	files, err := load(path, []string{
		".git", "/.git", "/.git/", ".gitignore", ".DS_Store", ".idea", "/.idea/", "/.idea",
	})

	if err != nil {
		return err
	}

	for _, fileName := range files {
		key := strings.TrimLeft(fileName, path)
		filePath := filepath.Clean(fileName)

		file, err := os.Open(filePath)
		if err != nil {
			return err
		}

		defer file.Close()

		data, err := io.ReadAll(file)
		if err != nil {
			return err
		}

		// data, err := os.ReadFile(fp)
		// if err != nil {
		// 	return err
		// }

		key = strings.Replace(key, ".json", "", 1)

		input[key] = data
	}

	return err
}

// load is a function that recursively walks through a directory and returns a list of file paths that are not ignored.

// Parameters:
// - dir: The directory path to be walked.
// - ignore: A list of file or directory names to be ignored during the walk.

// Returns:
// - []string: A slice of file paths that are not ignored.
// - error: An error indicating if there was an issue during the walk.

// Example Usage:
// files, err := load(dir, ignore)
// if err != nil {
//     // handle error
// }

// Note:
// - The ignore list should contain file or directory names that should be skipped during the walk.
// - The returned file paths are absolute paths.
func load(dir string, ignore []string) ([]string, error) {
	var files []string
	err := filepath.Walk(dir, func(path string, info os.FileInfo, e error) error {
		if e != nil {
			return e
		}
		ignored := false
		for _, item := range ignore {
			if strings.Contains(path, item) {
				ignored = true
			}
		}

		if !ignored {
			fileMod := info.Mode()

			if fileMod.IsRegular() {
				files = append(files, path)
			}
		}
		return nil
	})
	return files, err
}

// validateSchema is a function that validates the input against a JSON schema.

// Parameters:
// - input: The input to be validated.
// - schema: The JSON schema in byte array format.

// Returns:
// - []string: A slice of validation errors, if any.
// - error: An error indicating if the validation failed. If the validation fails, the error message will be "bad request".

// Example Usage:
// errs, err := validateSchema(input, schema)
// if err != nil {
//     // handle validation error
// }

// Note:
// - The input can be of any type.
// - The schema must be in JSON format.
func validateSchema(input interface{}, schema []byte) ([]string, error) {
	bytes, err := json.Marshal(input)
	if err != nil {
		return nil, err
	}

	sl := gojsonschema.NewStringLoader(string(schema))
	rsl := gojsonschema.NewStringLoader(string(bytes))

	res, err := gojsonschema.Validate(sl, rsl)
	if err != nil {
		return nil, err
	}

	if !res.Valid() {
		var errs []string
		for _, e := range res.Errors() {
			t := e.Type()
			switch t {
			case gojsonschema.KEY_PATTERN:
				errs = append(errs, fmt.Sprintf(formatError, e.Field()))
			case gojsonschema.KEY_REQUIRED:
				errs = append(errs, fmt.Sprintf("%v", e.Description()))
			case gojsonschema.KEY_ENUM:
				errs = append(
					errs,
					fmt.Sprintf("%v", strings.Replace(e.Description(), "\"", "'", -1)),
				)
			case "condition_else":
			case "condition_then":
			default:
				errs = append(errs, fmt.Sprintf("%v %s", e.Field(), e.Description()))
			}
		}
		return errs, errors.New("bad request")
	}

	return nil, nil
}

/*
ValidateInput is a function that validates the input against a schema.

Parameters:
- input: The input to be validated.
- schemas: A map of schema names to schema byte arrays.

Returns:
- error: An error indicating if the validation failed. If the validation fails, the error message will contain the specific validation errors.

Example Usage:

	err := ValidateInput(input, schemas)
	if err != nil {
		// handle validation error
	}

Note:
- The input must be a pointer to a struct.
- The schema for the input struct must be present in the schemas map, with the struct name as the key.
- The schema must be in JSON format.
*/
func ValidateInput(input interface{}, schemas map[string][]byte) error {
	val := reflect.ValueOf(input)
	if val.Kind() != reflect.Ptr {
		return ErrUnimplementedRequest
	}

	name := reflect.Indirect(val).Type().Name()
	schema, ok := schemas[name]
	if !ok {
		return ErrUnimplementedRequest
	}

	errs, err := validateSchema(input, schema)
	if err != nil {
		if len(errs) > 0 {
			return errors.New(strings.Join(errs, " , "))
		} else {
			return err
		}
	}

	return nil
}

// GetSchemaPath returns the path to the schema file for the specified module.

// Parameters:
// - module: The name of the module.

// Returns:
// - string: The path to the schema file.

// Example Usage:
// path := GetSchemaPath("myModule")
// fmt.Println(path) // Output: "./schemas/myModule"
func GetSchemaPath(module string) string {
	if runtime.GOOS == "windows" {
		return fmt.Sprintf(".\\schemas\\%s", module)
	}
	return fmt.Sprintf("./schemas/%s", module)
}
