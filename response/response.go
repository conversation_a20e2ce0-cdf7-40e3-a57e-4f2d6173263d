package response

import (
	"reflect"
)

type Response[T any] struct {
	Results int64  `json:"results"`
	Status  int    `json:"-"`
	Error   string `json:"error"`
	Err     error  `json:"-"`
	Result  T      `json:"result,omitempty"`
}

type ErrorResponse struct {
	Error string `json:"error"`
}

/*
NewError is a function that creates a new instance of the Response struct with an error message and status code.

Parameters:
- err (error): The error that occurred.
- status (int): The HTTP status code to be set in the response.

Returns:
- *Response[T]: A pointer to the newly created Response struct.

Example:

	err := errors.New("Something went wrong")
	response := NewError(err, 500)
*/
func NewError[T any](err error, status int) Response[T] {
	return Response[T]{
		Status:  status,
		Error:   err.Error(),
		Err:     err,
		Results: -1,
	}
}

func (r Response[T]) Prepare() Response[T] {
	if r.Err != nil {
		return r
	}

	var len int64
	k := r.Result

	switch reflect.TypeOf(r.Result).Kind() {
	case reflect.Slice:
		len = int64(reflect.ValueOf(k).Len())
	case reflect.Array:
		len = int64(reflect.ValueOf(k).Len())
	default:
		r.Results = 0
	}

	r.Results = len

	return r
}
